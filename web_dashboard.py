#!/usr/bin/env python3
"""
IP信誉恢复Web控制面板
提供Web界面来控制和监控IP信誉恢复脚本
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import subprocess
import json
import os
import time
import threading
from datetime import datetime
import psutil
import signal

app = Flask(__name__)

class IPReputationDashboard:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.running_processes = {}
        
    def run_command(self, cmd, background=False):
        """运行命令"""
        try:
            if background:
                process = subprocess.Popen(
                    cmd, 
                    shell=True, 
                    cwd=self.script_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )
                return True, process.pid, ""
            else:
                result = subprocess.run(
                    cmd, 
                    shell=True, 
                    cwd=self.script_dir,
                    capture_output=True, 
                    text=True,
                    timeout=30
                )
                return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def get_system_status(self):
        """获取系统状态"""
        try:
            # CPU和内存使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 检查服务状态
            service_status = self.check_service_status()
            
            # 检查脚本进程
            script_processes = self.get_script_processes()
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used": f"{memory.used / (1024**3):.1f}GB",
                "memory_total": f"{memory.total / (1024**3):.1f}GB",
                "service_status": service_status,
                "script_processes": script_processes,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            return {"error": str(e)}
    
    def check_service_status(self):
        """检查systemd服务状态"""
        try:
            result = subprocess.run(
                ["systemctl", "is-active", "ip-reputation-recovery"],
                capture_output=True,
                text=True
            )
            active = result.stdout.strip() == "active"
            
            result = subprocess.run(
                ["systemctl", "is-enabled", "ip-reputation-recovery"],
                capture_output=True,
                text=True
            )
            enabled = result.stdout.strip() == "enabled"
            
            return {"active": active, "enabled": enabled}
        except:
            return {"active": False, "enabled": False}
    
    def get_script_processes(self):
        """获取脚本进程信息"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'ip_reputation_recovery' in cmdline:
                        processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'start_time': datetime.fromtimestamp(proc.info['create_time']).strftime("%H:%M:%S")
                        })
                except:
                    continue
        except:
            pass
        return processes
    
    def get_log_content(self, log_file, lines=50):
        """获取日志内容"""
        log_path = os.path.join(self.script_dir, log_file)
        try:
            if os.path.exists(log_path):
                with open(log_path, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    return ''.join(all_lines[-lines:])
            else:
                return f"日志文件 {log_file} 不存在"
        except Exception as e:
            return f"读取日志失败: {str(e)}"
    
    def get_config(self):
        """获取配置信息"""
        configs = {}
        config_files = ['config.json', 'config_advanced.json']
        
        for config_file in config_files:
            config_path = os.path.join(self.script_dir, config_file)
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        configs[config_file] = json.load(f)
                except Exception as e:
                    configs[config_file] = {"error": str(e)}
        
        return configs

dashboard = IPReputationDashboard()

@app.route('/')
def index():
    """主页"""
    return render_template('dashboard.html')

@app.route('/api/status')
def api_status():
    """获取系统状态API"""
    return jsonify(dashboard.get_system_status())

@app.route('/api/logs/<log_type>')
def api_logs(log_type):
    """获取日志API"""
    log_files = {
        'basic': 'ip_recovery.log',
        'advanced': 'ip_recovery_advanced.log',
        'checker': 'ip_reputation_check.log'
    }
    
    log_file = log_files.get(log_type, 'ip_recovery.log')
    lines = request.args.get('lines', 50, type=int)
    content = dashboard.get_log_content(log_file, lines)
    
    return jsonify({"content": content})

@app.route('/api/config')
def api_config():
    """获取配置API"""
    return jsonify(dashboard.get_config())

@app.route('/api/run/<script_type>')
def api_run(script_type):
    """运行脚本API"""
    mode = request.args.get('mode', 'once')  # once 或 continuous
    
    scripts = {
        'basic': 'python3 ip_reputation_recovery.py',
        'advanced': 'python3 ip_reputation_recovery_advanced.py',
        'checker': 'python3 ip_reputation_checker.py'
    }
    
    if script_type not in scripts:
        return jsonify({"success": False, "message": "无效的脚本类型"})
    
    cmd = f"{scripts[script_type]} --{mode}"
    
    if mode == 'continuous':
        success, pid, error = dashboard.run_command(cmd, background=True)
        if success:
            dashboard.running_processes[script_type] = pid
            return jsonify({"success": True, "message": f"脚本已在后台启动，PID: {pid}"})
        else:
            return jsonify({"success": False, "message": f"启动失败: {error}"})
    else:
        success, output, error = dashboard.run_command(cmd)
        return jsonify({
            "success": success,
            "message": "执行完成" if success else f"执行失败: {error}",
            "output": output
        })

@app.route('/api/stop/<script_type>')
def api_stop(script_type):
    """停止脚本API"""
    if script_type in dashboard.running_processes:
        try:
            pid = dashboard.running_processes[script_type]
            os.killpg(os.getpgid(pid), signal.SIGTERM)
            del dashboard.running_processes[script_type]
            return jsonify({"success": True, "message": f"脚本已停止"})
        except Exception as e:
            return jsonify({"success": False, "message": f"停止失败: {str(e)}"})
    else:
        return jsonify({"success": False, "message": "脚本未在运行"})

@app.route('/api/service/<action>')
def api_service(action):
    """服务控制API"""
    actions = {
        'start': 'systemctl start ip-reputation-recovery',
        'stop': 'systemctl stop ip-reputation-recovery',
        'restart': 'systemctl restart ip-reputation-recovery',
        'enable': 'systemctl enable ip-reputation-recovery',
        'disable': 'systemctl disable ip-reputation-recovery'
    }
    
    if action not in actions:
        return jsonify({"success": False, "message": "无效的操作"})
    
    success, output, error = dashboard.run_command(f"sudo {actions[action]}")
    return jsonify({
        "success": success,
        "message": "操作成功" if success else f"操作失败: {error}"
    })

@app.route('/api/test')
def api_test():
    """运行测试API"""
    success, output, error = dashboard.run_command("python3 test_debian12.py")
    return jsonify({
        "success": success,
        "output": output,
        "error": error
    })

if __name__ == '__main__':
    print("🌐 启动IP信誉恢复Web控制面板")
    print("📱 访问地址: http://localhost:5000")
    print("🔒 如需外部访问，请修改host参数")
    
    # 检查是否安装了Flask
    try:
        import flask
        print(f"✅ Flask版本: {flask.__version__}")
    except ImportError:
        print("❌ 未安装Flask，请运行: pip3 install flask psutil")
        exit(1)
    
    app.run(host='0.0.0.0', port=5000, debug=False)
