# IP信誉恢复脚本 (IPpurifier)

一个专为Debian 12系统设计的IP信誉自动恢复工具，通过模拟正常用户浏览行为来提升被误标记或黑名单IP的信誉分数。

## 🎯 核心功能

- **智能模拟**: 模拟真实用户的网络浏览行为
- **多平台支持**: 针对AbuseIPDB、Talos、Spamhaus等主流信誉数据库
- **双版本设计**: 基础版本(轻量)和高级版本(真实浏览器)
- **实时监控**: 自动检测IP信誉状态变化
- **灵活部署**: 支持手动、定时任务、系统服务等多种运行方式

## 📁 项目结构

```
IPpurifier/
├── 📄 README.md                           # 项目说明
├── 📄 USAGE_GUIDE.md                      # 详细使用指南
├── 📄 IP_REPUTATION_RECOVERY_DESIGN.md    # 设计理念文档
├── 🔧 install.sh                          # 一键安装脚本
├── 🛠️ fix_debian12.sh                     # Debian 12环境修复脚本
├── 🎮 start_recovery.sh                   # 交互式控制面板
├── 🧪 test_basic.py                       # 基础功能测试
├── 🧪 test_debian12.py                    # Debian 12环境测试
├── ⚙️ config.json                         # 基础版本配置
├── 🐍 ip_reputation_recovery.py           # 基础版本主脚本
├── 🌐 ip_reputation_recovery_advanced.py  # 高级版本主脚本
├── 🔍 ip_reputation_checker.py            # IP信誉检测脚本
├── 🚀 run_basic.sh                        # 基础版本包装脚本
├── 🚀 run_advanced.sh                     # 高级版本包装脚本
└── 🚀 run_checker.sh                      # IP检测包装脚本
```

## 🚀 快速开始

### Debian 12 用户特别说明
Debian 12引入了新的Python包管理限制，请使用专门的修复脚本：

```bash
# 1. 运行Debian 12修复脚本
chmod +x fix_debian12.sh
./fix_debian12.sh

# 2. 测试环境
python3 test_debian12.py

# 3. 使用包装脚本运行（推荐）
./run_basic.sh --once
./run_checker.sh
```

### 通用安装方法

#### 1. 一键安装
```bash
# 克隆项目
git clone <repository_url>
cd IPpurifier

# 运行安装脚本
chmod +x install.sh
./install.sh
```

#### 2. 基础测试
```bash
# 测试环境和配置
python3 test_basic.py

# Debian 12用户使用
python3 test_debian12.py

# 运行一次基础版本
python3 ip_reputation_recovery.py --once
```

#### 3. 检查IP信誉
```bash
# 检查当前IP信誉状态
python3 ip_reputation_checker.py
```

#### 4. 启动交互式面板
```bash
# 使用图形化控制面板
./start_recovery.sh
```

## 📊 脚本对比

| 特性 | 基础版本 | 高级版本 |
|------|----------|----------|
| 资源占用 | 低 | 中等 |
| 模拟真实度 | 中等 | 高 |
| 稳定性 | 高 | 中等 |
| 安装复杂度 | 简单 | 中等 |
| 适用场景 | 轻度-中度问题 | 中度-重度问题 |
| 依赖 | requests | requests + selenium + chrome |

## 🎯 使用策略

### 轻度恢复 (误报/轻微问题)
```bash
# 使用基础版本，每2-4小时运行一次
0 */2 * * * cd ~/ip_reputation_recovery && python3 ip_reputation_recovery.py --once
```

### 中度恢复 (多次举报)
```bash
# 先基础版本1周，再切换高级版本
# 基础版本
0 */2 * * * cd ~/ip_reputation_recovery && python3 ip_reputation_recovery.py --once
# 一周后切换为高级版本
0 * * * * cd ~/ip_reputation_recovery && python3 ip_reputation_recovery_advanced.py --once
```

### 重度恢复 (严重黑名单)
```bash
# 使用高级版本，每小时运行
0 * * * * cd ~/ip_reputation_recovery && python3 ip_reputation_recovery_advanced.py --once
```

## ⚙️ 配置说明

### 基础配置 (config.json)
```json
{
  "sites_per_session": 8,    // 每次访问网站数量
  "session_interval": 3600,  // 会话间隔(秒)
  "min_delay": 5,            // 最小延迟(秒)
  "max_delay": 20            // 最大延迟(秒)
}
```

### 高级配置 (config_advanced.json)
```json
{
  "headless": true,          // 无头模式
  "scroll_behavior": true,   // 模拟滚动
  "random_clicks": true,     // 模拟点击
  "stay_time_min": 3,        // 最小停留时间
  "stay_time_max": 10        // 最大停留时间
}
```

## 📈 监控和日志

### 查看实时日志
```bash
# 基础版本日志
tail -f ~/ip_reputation_recovery/ip_recovery.log

# 高级版本日志
tail -f ~/ip_reputation_recovery/ip_recovery_advanced.log

# 系统服务日志
sudo journalctl -u ip-reputation-recovery -f
```

### IP信誉监控
```bash
# 单次检查
python3 ip_reputation_checker.py

# 持续监控(每小时检查一次)
python3 ip_reputation_checker.py --monitor --interval 3600
```

## 🔧 系统服务管理

```bash
# 启动服务
sudo systemctl start ip-reputation-recovery

# 停止服务
sudo systemctl stop ip-reputation-recovery

# 查看状态
sudo systemctl status ip-reputation-recovery

# 开机自启
sudo systemctl enable ip-reputation-recovery
```

## ⚠️ 重要注意事项

### 安全要求
1. **清理服务器**: 确保移除所有恶意软件和后门
2. **关闭危险端口**: 停用不必要的服务
3. **系统更新**: 保持系统和软件最新

### 使用限制
1. **适度频率**: 避免过于频繁的访问
2. **合理期望**: IP信誉恢复需要1-4周时间
3. **合规使用**: 仅用于合法IP信誉恢复

### 效果评估
- 使用IP信誉检测脚本定期监控
- 在多个平台验证IP状态
- 测试实际应用场景(邮件发送等)

## 🛠️ 故障排除

### 常见问题
1. **网络连接问题**: 检查防火墙和DNS设置
2. **Chrome问题**: 重新安装Chrome和ChromeDriver
3. **权限问题**: 确保脚本有执行权限
4. **依赖问题**: 重新安装Python包

### 调试命令
```bash
# 测试基础功能
python3 test_basic.py

# 检查配置文件
python3 -m json.tool config.json

# 测试网络连接
curl -I https://www.google.com
```

## 📞 技术支持

如遇问题，请：
1. 运行 `python3 test_basic.py` 检查环境
2. 查看日志文件获取详细错误信息
3. 参考 `USAGE_GUIDE.md` 获取详细说明
4. 检查网络连接和系统环境

## 📄 许可证

本项目仅用于合法的IP信誉恢复目的。使用者需确保遵守相关法律法规和网站使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**免责声明**: 本工具仅用于合法的IP信誉恢复，不得用于任何恶意目的。使用者需自行承担使用风险并遵守相关法律法规。
