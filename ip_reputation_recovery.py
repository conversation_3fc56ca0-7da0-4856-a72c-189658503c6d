#!/usr/bin/env python3
"""
IP信誉恢复脚本 - 模拟正常用户浏览行为
适用于Debian 12系统
"""

import requests
import random
import time
import json
import logging
import argparse
import sys
from datetime import datetime
from urllib.parse import urlparse
import os

class IPReputationRecovery:
    def __init__(self, config_file="config.json"):
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.session = requests.Session()
        
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "sites": [
                "https://www.google.com",
                "https://www.bing.com", 
                "https://www.youtube.com",
                "https://www.amazon.com",
                "https://www.wikipedia.org",
                "https://www.github.com",
                "https://www.stackoverflow.com",
                "https://www.reddit.com",
                "https://www.twitter.com",
                "https://www.facebook.com",
                "https://www.baidu.com",
                "https://www.zhihu.com",
                "https://www.bilibili.com",
                "https://news.ycombinator.com",
                "https://www.bbc.com"
            ],
            "user_agents": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0"
            ],
            "min_delay": 5,
            "max_delay": 20,
            "timeout": 10,
            "max_retries": 3,
            "log_file": "ip_recovery.log",
            "sites_per_session": 8,
            "session_interval": 3600
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            print(f"已创建默认配置文件: {config_file}")
            
        return default_config
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config['log_file'], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(self.config['user_agents'])
    
    def get_random_delay(self):
        """获取随机延迟时间"""
        return random.uniform(self.config['min_delay'], self.config['max_delay'])
    
    def visit_site(self, url):
        """访问单个网站"""
        try:
            # 设置随机User-Agent
            headers = {
                'User-Agent': self.get_random_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            # 发送请求
            response = self.session.get(
                url, 
                headers=headers, 
                timeout=self.config['timeout'],
                allow_redirects=True
            )
            
            # 记录访问结果
            self.logger.info(f"访问成功: {url} | 状态码: {response.status_code} | UA: {headers['User-Agent'][:50]}...")
            
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.warning(f"访问失败: {url} | 错误: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"未知错误: {url} | 错误: {str(e)}")
            return False
    
    def run_session(self):
        """运行一次浏览会话"""
        self.logger.info("=" * 60)
        self.logger.info("开始新的浏览会话")
        
        # 随机选择要访问的网站
        sites_to_visit = random.sample(
            self.config['sites'], 
            min(self.config['sites_per_session'], len(self.config['sites']))
        )
        
        # 随机打乱访问顺序
        random.shuffle(sites_to_visit)
        
        success_count = 0
        total_count = len(sites_to_visit)
        
        for i, site in enumerate(sites_to_visit, 1):
            self.logger.info(f"正在访问 ({i}/{total_count}): {site}")
            
            if self.visit_site(site):
                success_count += 1
            
            # 随机延迟（除了最后一个网站）
            if i < total_count:
                delay = self.get_random_delay()
                self.logger.info(f"等待 {delay:.1f} 秒...")
                time.sleep(delay)
        
        self.logger.info(f"会话完成: 成功访问 {success_count}/{total_count} 个网站")
        return success_count, total_count
    
    def run_continuous(self):
        """持续运行模式"""
        self.logger.info("启动持续运行模式")
        self.logger.info(f"会话间隔: {self.config['session_interval']} 秒")
        
        session_count = 0
        
        try:
            while True:
                session_count += 1
                self.logger.info(f"第 {session_count} 次会话")
                
                success, total = self.run_session()
                
                self.logger.info(f"等待下次会话... ({self.config['session_interval']} 秒)")
                time.sleep(self.config['session_interval'])
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在停止...")
        except Exception as e:
            self.logger.error(f"运行时错误: {e}")
    
    def run_once(self):
        """运行一次"""
        self.logger.info("运行单次浏览会话")
        return self.run_session()

def main():
    parser = argparse.ArgumentParser(description='IP信誉恢复脚本')
    parser.add_argument('--config', '-c', default='config.json', help='配置文件路径')
    parser.add_argument('--continuous', '-d', action='store_true', help='持续运行模式')
    parser.add_argument('--once', '-o', action='store_true', help='运行一次后退出')
    
    args = parser.parse_args()
    
    # 创建恢复器实例
    recovery = IPReputationRecovery(args.config)
    
    if args.continuous:
        recovery.run_continuous()
    elif args.once:
        recovery.run_once()
    else:
        # 默认运行一次
        recovery.run_once()

if __name__ == "__main__":
    main()
