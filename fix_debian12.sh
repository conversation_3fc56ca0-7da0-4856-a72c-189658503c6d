#!/bin/bash

# Debian 12 Python环境修复脚本
# 解决externally-managed-environment问题

set -e

echo "=========================================="
echo "Debian 12 Python环境修复脚本"
echo "=========================================="

# 检查是否为Debian 12
if [ -f /etc/debian_version ]; then
    DEBIAN_VERSION=$(cat /etc/debian_version)
    echo "检测到Debian版本: $DEBIAN_VERSION"
else
    echo "警告: 未检测到Debian系统"
fi

# 方案1: 使用系统包管理器
echo ""
echo "方案1: 安装系统级Python包"
echo "----------------------------------------"
sudo apt update
sudo apt install -y python3-requests python3-venv

# 检查是否需要安装Selenium相关包
if [ -f "ip_reputation_recovery_advanced.py" ]; then
    echo "检测到高级版本脚本，安装Selenium相关包..."
    
    # 尝试安装系统级Selenium
    if sudo apt install -y python3-selenium 2>/dev/null; then
        echo "✅ 已安装系统级python3-selenium"
    else
        echo "⚠️  系统级python3-selenium不可用"
    fi
    
    # 安装Chrome和ChromeDriver
    if ! command -v google-chrome &> /dev/null; then
        echo "安装Google Chrome..."
        wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo gpg --dearmor -o /usr/share/keyrings/google-chrome-keyring.gpg
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/google-chrome-keyring.gpg] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
        sudo apt update
        sudo apt install -y google-chrome-stable
    fi
    
    sudo apt install -y chromium-driver
fi

# 方案2: 创建虚拟环境
echo ""
echo "方案2: 创建Python虚拟环境"
echo "----------------------------------------"
VENV_DIR="$HOME/ip_reputation_recovery_venv"

if [ ! -d "$VENV_DIR" ]; then
    echo "创建虚拟环境: $VENV_DIR"
    python3 -m venv "$VENV_DIR"
else
    echo "虚拟环境已存在: $VENV_DIR"
fi

# 激活虚拟环境并安装包
echo "在虚拟环境中安装Python包..."
source "$VENV_DIR/bin/activate"
pip install --upgrade pip
pip install requests

if [ -f "ip_reputation_recovery_advanced.py" ]; then
    pip install selenium
fi

deactivate

# 方案3: 创建包装脚本
echo ""
echo "方案3: 创建包装脚本"
echo "----------------------------------------"

# 创建基础版本包装脚本
cat > "run_basic.sh" <<'EOF'
#!/bin/bash
# 基础版本运行脚本 - 自动选择最佳Python环境

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 尝试使用虚拟环境
if [ -d "$HOME/ip_reputation_recovery_venv" ]; then
    echo "使用虚拟环境运行..."
    source "$HOME/ip_reputation_recovery_venv/bin/activate"
    python ip_reputation_recovery.py "$@"
    deactivate
else
    # 使用系统Python
    echo "使用系统Python运行..."
    python3 ip_reputation_recovery.py "$@"
fi
EOF

chmod +x run_basic.sh

# 创建高级版本包装脚本
if [ -f "ip_reputation_recovery_advanced.py" ]; then
    cat > "run_advanced.sh" <<'EOF'
#!/bin/bash
# 高级版本运行脚本 - 自动选择最佳Python环境

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 尝试使用虚拟环境
if [ -d "$HOME/ip_reputation_recovery_venv" ]; then
    echo "使用虚拟环境运行..."
    source "$HOME/ip_reputation_recovery_venv/bin/activate"
    python ip_reputation_recovery_advanced.py "$@"
    deactivate
else
    # 使用系统Python
    echo "使用系统Python运行..."
    python3 ip_reputation_recovery_advanced.py "$@"
fi
EOF

    chmod +x run_advanced.sh
fi

# 创建IP检测包装脚本
cat > "run_checker.sh" <<'EOF'
#!/bin/bash
# IP信誉检测运行脚本 - 自动选择最佳Python环境

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 尝试使用虚拟环境
if [ -d "$HOME/ip_reputation_recovery_venv" ]; then
    echo "使用虚拟环境运行..."
    source "$HOME/ip_reputation_recovery_venv/bin/activate"
    python ip_reputation_checker.py "$@"
    deactivate
else
    # 使用系统Python
    echo "使用系统Python运行..."
    python3 ip_reputation_checker.py "$@"
fi
EOF

chmod +x run_checker.sh

# 更新systemd服务文件
echo ""
echo "更新systemd服务配置..."
if [ -f "/etc/systemd/system/ip-reputation-recovery.service" ]; then
    sudo tee /etc/systemd/system/ip-reputation-recovery.service > /dev/null <<EOF
[Unit]
Description=IP Reputation Recovery Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
ExecStart=$(pwd)/run_basic.sh --continuous
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    echo "✅ systemd服务配置已更新"
fi

# 测试环境
echo ""
echo "测试Python环境..."
echo "----------------------------------------"

echo "测试系统Python环境:"
if python3 -c "import requests; print('✅ requests可用')" 2>/dev/null; then
    echo "✅ 系统Python环境正常"
    SYSTEM_PYTHON_OK=true
else
    echo "❌ 系统Python环境缺少依赖"
    SYSTEM_PYTHON_OK=false
fi

echo ""
echo "测试虚拟环境:"
if [ -d "$VENV_DIR" ]; then
    source "$VENV_DIR/bin/activate"
    if python -c "import requests; print('✅ requests可用')" 2>/dev/null; then
        echo "✅ 虚拟环境正常"
        VENV_OK=true
    else
        echo "❌ 虚拟环境异常"
        VENV_OK=false
    fi
    deactivate
else
    echo "❌ 虚拟环境不存在"
    VENV_OK=false
fi

# 生成使用说明
echo ""
echo "=========================================="
echo "修复完成！使用说明："
echo "=========================================="

if [ "$SYSTEM_PYTHON_OK" = true ]; then
    echo "✅ 系统Python环境可用，推荐使用方式："
    echo "   python3 ip_reputation_recovery.py --once"
    echo "   python3 ip_reputation_checker.py"
fi

if [ "$VENV_OK" = true ]; then
    echo "✅ 虚拟环境可用，推荐使用方式："
    echo "   ./run_basic.sh --once"
    echo "   ./run_checker.sh"
    if [ -f "run_advanced.sh" ]; then
        echo "   ./run_advanced.sh --once"
    fi
fi

echo ""
echo "包装脚本（推荐）："
echo "   ./run_basic.sh --once        # 运行基础版本一次"
echo "   ./run_basic.sh --continuous  # 基础版本持续运行"
echo "   ./run_checker.sh             # 检查IP信誉"

if [ -f "run_advanced.sh" ]; then
    echo "   ./run_advanced.sh --once     # 运行高级版本一次"
fi

echo ""
echo "systemd服务："
echo "   sudo systemctl start ip-reputation-recovery"
echo "   sudo systemctl enable ip-reputation-recovery"

echo ""
echo "🎉 Debian 12环境修复完成！"
