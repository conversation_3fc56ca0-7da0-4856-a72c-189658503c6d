#!/usr/bin/env python3
"""
Debian 12 环境测试脚本
专门测试在Debian 12系统上的Python环境和依赖
"""

import sys
import os
import subprocess
import json

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_system_info():
    """测试系统信息"""
    print("🖥️  系统信息检查...")
    
    # 检查Debian版本
    if os.path.exists('/etc/debian_version'):
        with open('/etc/debian_version', 'r') as f:
            debian_version = f.read().strip()
        print(f"   Debian版本: {debian_version}")
    
    # 检查Python版本
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    # 检查是否为root用户
    if os.geteuid() == 0:
        print("   ⚠️  当前为root用户")
    else:
        print(f"   当前用户: {os.getenv('USER', 'unknown')}")
    
    return True

def test_python_packages():
    """测试Python包"""
    print("\n📦 Python包检查...")
    
    packages = {
        'requests': 'HTTP请求库',
        'json': 'JSON处理（内置）',
        'time': '时间处理（内置）',
        'random': '随机数（内置）'
    }
    
    available_packages = []
    missing_packages = []
    
    for package, description in packages.items():
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
            available_packages.append(package)
        except ImportError:
            print(f"   ❌ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    # 特别检查Selenium
    try:
        import selenium
        print(f"   ✅ selenium - 浏览器自动化 (版本: {selenium.__version__})")
        available_packages.append('selenium')
    except ImportError:
        print("   ⚠️  selenium - 浏览器自动化 (未安装，高级版本需要)")
        missing_packages.append('selenium')
    
    return len(missing_packages) == 0 or 'requests' in available_packages

def test_virtual_environment():
    """测试虚拟环境"""
    print("\n🐍 虚拟环境检查...")
    
    venv_path = os.path.expanduser("~/ip_reputation_recovery_venv")
    
    if os.path.exists(venv_path):
        print(f"   ✅ 虚拟环境存在: {venv_path}")
        
        # 测试虚拟环境中的包
        python_path = os.path.join(venv_path, "bin", "python")
        if os.path.exists(python_path):
            success, stdout, stderr = run_command(f"{python_path} -c 'import requests; print(requests.__version__)'")
            if success:
                print(f"   ✅ 虚拟环境中requests可用: {stdout.strip()}")
                return True
            else:
                print("   ❌ 虚拟环境中requests不可用")
        else:
            print("   ❌ 虚拟环境Python解释器不存在")
    else:
        print("   ⚠️  虚拟环境不存在")
    
    return False

def test_system_packages():
    """测试系统级Python包"""
    print("\n📋 系统级包检查...")
    
    # 检查系统级requests
    success, stdout, stderr = run_command("python3 -c 'import requests; print(requests.__version__)'")
    if success:
        print(f"   ✅ 系统级requests可用: {stdout.strip()}")
        return True
    else:
        print("   ❌ 系统级requests不可用")
        if "externally-managed-environment" in stderr:
            print("   ℹ️  检测到Debian 12的外部管理环境限制")
    
    return False

def test_chrome_selenium():
    """测试Chrome和Selenium环境"""
    print("\n🌐 Chrome/Selenium环境检查...")
    
    # 检查Chrome
    success, stdout, stderr = run_command("google-chrome --version")
    if success:
        print(f"   ✅ Chrome可用: {stdout.strip()}")
    else:
        print("   ❌ Chrome不可用")
    
    # 检查ChromeDriver
    success, stdout, stderr = run_command("chromedriver --version")
    if success:
        print(f"   ✅ ChromeDriver可用: {stdout.strip()}")
    else:
        # 尝试chromium-driver
        success, stdout, stderr = run_command("chromium-driver --version")
        if success:
            print(f"   ✅ Chromium-driver可用: {stdout.strip()}")
        else:
            print("   ❌ ChromeDriver/Chromium-driver不可用")
    
    return True

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌍 网络连接测试...")
    
    try:
        import requests
        
        test_urls = [
            "https://www.google.com",
            "https://httpbin.org/get"
        ]
        
        success_count = 0
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"   ✅ {url} - 连接成功")
                    success_count += 1
                else:
                    print(f"   ⚠️  {url} - 状态码: {response.status_code}")
            except Exception as e:
                print(f"   ❌ {url} - 连接失败: {str(e)[:50]}")
        
        return success_count > 0
        
    except ImportError:
        print("   ❌ 无法导入requests库，跳过网络测试")
        return False

def test_config_files():
    """测试配置文件"""
    print("\n⚙️  配置文件检查...")
    
    config_files = ['config.json']
    if os.path.exists('ip_reputation_recovery_advanced.py'):
        config_files.append('config_advanced.json')
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"   ✅ {config_file} - 格式正确")
            except json.JSONDecodeError:
                print(f"   ❌ {config_file} - JSON格式错误")
            except Exception as e:
                print(f"   ❌ {config_file} - 读取失败: {e}")
        else:
            print(f"   ⚠️  {config_file} - 文件不存在")
    
    return True

def test_wrapper_scripts():
    """测试包装脚本"""
    print("\n🔧 包装脚本检查...")
    
    wrapper_scripts = ['run_basic.sh', 'run_checker.sh']
    if os.path.exists('ip_reputation_recovery_advanced.py'):
        wrapper_scripts.append('run_advanced.sh')
    
    for script in wrapper_scripts:
        if os.path.exists(script):
            if os.access(script, os.X_OK):
                print(f"   ✅ {script} - 存在且可执行")
            else:
                print(f"   ⚠️  {script} - 存在但不可执行")
        else:
            print(f"   ❌ {script} - 不存在")
    
    return True

def generate_recommendations():
    """生成使用建议"""
    print("\n💡 使用建议:")
    print("=" * 50)
    
    # 检查可用的运行方式
    has_system_requests = False
    has_venv = False
    has_wrappers = False
    
    try:
        import requests
        has_system_requests = True
    except ImportError:
        pass
    
    if os.path.exists(os.path.expanduser("~/ip_reputation_recovery_venv")):
        has_venv = True
    
    if os.path.exists('run_basic.sh'):
        has_wrappers = True
    
    if has_wrappers:
        print("🎯 推荐使用包装脚本（自动选择最佳环境）:")
        print("   ./run_basic.sh --once        # 运行基础版本一次")
        print("   ./run_checker.sh             # 检查IP信誉")
        if os.path.exists('run_advanced.sh'):
            print("   ./run_advanced.sh --once     # 运行高级版本一次")
    
    if has_system_requests:
        print("\n✅ 系统Python环境可用:")
        print("   python3 ip_reputation_recovery.py --once")
        print("   python3 ip_reputation_checker.py")
    
    if has_venv:
        print("\n🐍 虚拟环境可用:")
        print("   source ~/ip_reputation_recovery_venv/bin/activate")
        print("   python ip_reputation_recovery.py --once")
        print("   deactivate")
    
    if not has_system_requests and not has_venv and not has_wrappers:
        print("\n⚠️  需要修复Python环境:")
        print("   chmod +x fix_debian12.sh")
        print("   ./fix_debian12.sh")

def main():
    """主测试函数"""
    print("🔍 Debian 12 IP信誉恢复脚本环境测试")
    print("=" * 60)
    
    tests = [
        ("系统信息", test_system_info),
        ("Python包", test_python_packages),
        ("虚拟环境", test_virtual_environment),
        ("系统级包", test_system_packages),
        ("Chrome/Selenium", test_chrome_selenium),
        ("网络连接", test_network_connectivity),
        ("配置文件", test_config_files),
        ("包装脚本", test_wrapper_scripts)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ {test_name}测试出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    # 生成建议
    generate_recommendations()
    
    return passed >= total - 2  # 允许2个测试失败

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
