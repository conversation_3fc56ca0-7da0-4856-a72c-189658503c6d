#!/bin/bash

# IP信誉恢复快速启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示菜单
show_menu() {
    clear
    echo -e "${BLUE}=========================================="
    echo -e "       IP信誉恢复脚本控制面板"
    echo -e "==========================================${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 运行基础版本（一次）"
    echo -e "${GREEN}2.${NC} 运行基础版本（持续模式）"
    
    if [ -f "ip_reputation_recovery_advanced.py" ]; then
        echo -e "${GREEN}3.${NC} 运行高级版本（一次）"
        echo -e "${GREEN}4.${NC} 运行高级版本（持续模式）"
    else
        echo -e "${YELLOW}3.${NC} 高级版本（未安装）"
        echo -e "${YELLOW}4.${NC} 高级版本（未安装）"
    fi
    
    echo -e "${GREEN}5.${NC} 检查IP信誉状态"
    echo -e "${GREEN}6.${NC} 启动IP信誉监控"
    echo -e "${GREEN}7.${NC} 查看日志"
    echo -e "${GREEN}8.${NC} 管理systemd服务"
    echo -e "${GREEN}9.${NC} 编辑配置文件"
    echo -e "${RED}0.${NC} 退出"
    echo ""
}

# 检查IP信誉
check_reputation() {
    echo -e "${BLUE}正在检查IP信誉状态...${NC}"
    python3 ip_reputation_checker.py
    echo ""
    read -p "按回车键继续..."
}

# 启动监控
start_monitoring() {
    echo -e "${BLUE}启动IP信誉监控模式${NC}"
    echo "监控将每小时检查一次IP信誉状态"
    echo "按 Ctrl+C 停止监控"
    echo ""
    python3 ip_reputation_checker.py --monitor --interval 3600
}

# 查看日志
view_logs() {
    echo -e "${BLUE}选择要查看的日志:${NC}"
    echo "1. 基础版本日志"
    if [ -f "ip_recovery_advanced.log" ]; then
        echo "2. 高级版本日志"
    fi
    echo "3. IP信誉检测日志"
    echo "4. 系统服务日志"
    echo ""
    read -p "请选择 (1-4): " log_choice
    
    case $log_choice in
        1)
            if [ -f "ip_recovery.log" ]; then
                tail -f ip_recovery.log
            else
                echo -e "${RED}日志文件不存在${NC}"
            fi
            ;;
        2)
            if [ -f "ip_recovery_advanced.log" ]; then
                tail -f ip_recovery_advanced.log
            else
                echo -e "${RED}日志文件不存在${NC}"
            fi
            ;;
        3)
            if [ -f "ip_reputation_check.log" ]; then
                tail -f ip_reputation_check.log
            else
                echo -e "${RED}日志文件不存在${NC}"
            fi
            ;;
        4)
            sudo journalctl -u ip-reputation-recovery -f
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 管理systemd服务
manage_service() {
    echo -e "${BLUE}systemd服务管理:${NC}"
    echo "1. 启动服务"
    echo "2. 停止服务"
    echo "3. 重启服务"
    echo "4. 查看服务状态"
    echo "5. 启用开机自启"
    echo "6. 禁用开机自启"
    echo ""
    read -p "请选择 (1-6): " service_choice
    
    case $service_choice in
        1)
            sudo systemctl start ip-reputation-recovery
            echo -e "${GREEN}服务已启动${NC}"
            ;;
        2)
            sudo systemctl stop ip-reputation-recovery
            echo -e "${YELLOW}服务已停止${NC}"
            ;;
        3)
            sudo systemctl restart ip-reputation-recovery
            echo -e "${GREEN}服务已重启${NC}"
            ;;
        4)
            sudo systemctl status ip-reputation-recovery
            ;;
        5)
            sudo systemctl enable ip-reputation-recovery
            echo -e "${GREEN}已启用开机自启${NC}"
            ;;
        6)
            sudo systemctl disable ip-reputation-recovery
            echo -e "${YELLOW}已禁用开机自启${NC}"
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
    echo ""
    read -p "按回车键继续..."
}

# 编辑配置文件
edit_config() {
    echo -e "${BLUE}选择要编辑的配置文件:${NC}"
    echo "1. 基础版本配置 (config.json)"
    if [ -f "config_advanced.json" ]; then
        echo "2. 高级版本配置 (config_advanced.json)"
    fi
    echo ""
    read -p "请选择 (1-2): " config_choice
    
    case $config_choice in
        1)
            if command -v nano &> /dev/null; then
                nano config.json
            elif command -v vim &> /dev/null; then
                vim config.json
            else
                echo -e "${RED}未找到文本编辑器${NC}"
            fi
            ;;
        2)
            if [ -f "config_advanced.json" ]; then
                if command -v nano &> /dev/null; then
                    nano config_advanced.json
                elif command -v vim &> /dev/null; then
                    vim config_advanced.json
                else
                    echo -e "${RED}未找到文本编辑器${NC}"
                fi
            else
                echo -e "${RED}高级版本配置文件不存在${NC}"
            fi
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 主循环
while true; do
    show_menu
    read -p "请选择操作 (0-9): " choice
    
    case $choice in
        1)
            echo -e "${BLUE}运行基础版本（一次）...${NC}"
            python3 ip_reputation_recovery.py --once
            echo ""
            read -p "按回车键继续..."
            ;;
        2)
            echo -e "${BLUE}运行基础版本（持续模式）...${NC}"
            echo "按 Ctrl+C 停止运行"
            echo ""
            python3 ip_reputation_recovery.py --continuous
            ;;
        3)
            if [ -f "ip_reputation_recovery_advanced.py" ]; then
                echo -e "${BLUE}运行高级版本（一次）...${NC}"
                python3 ip_reputation_recovery_advanced.py --once
                echo ""
                read -p "按回车键继续..."
            else
                echo -e "${RED}高级版本未安装${NC}"
                read -p "按回车键继续..."
            fi
            ;;
        4)
            if [ -f "ip_reputation_recovery_advanced.py" ]; then
                echo -e "${BLUE}运行高级版本（持续模式）...${NC}"
                echo "按 Ctrl+C 停止运行"
                echo ""
                python3 ip_reputation_recovery_advanced.py --continuous
            else
                echo -e "${RED}高级版本未安装${NC}"
                read -p "按回车键继续..."
            fi
            ;;
        5)
            check_reputation
            ;;
        6)
            start_monitoring
            ;;
        7)
            view_logs
            ;;
        8)
            manage_service
            ;;
        9)
            edit_config
            ;;
        0)
            echo -e "${GREEN}再见！${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选择，请重新输入${NC}"
            sleep 1
            ;;
    esac
done
