# IP信誉恢复脚本完整使用指南

## 📋 目录
1. [快速开始](#快速开始)
2. [脚本说明](#脚本说明)
3. [安装步骤](#安装步骤)
4. [使用方法](#使用方法)
5. [配置说明](#配置说明)
6. [监控和日志](#监控和日志)
7. [最佳实践](#最佳实践)
8. [故障排除](#故障排除)
9. [注意事项](#注意事项)

## 🚀 快速开始

### 1. 一键安装
```bash
# 下载并运行安装脚本
chmod +x install.sh
./install.sh
```

### 2. 快速测试
```bash
cd ~/ip_reputation_recovery
python3 ip_reputation_recovery.py --once
```

### 3. 检查IP信誉
```bash
python3 ip_reputation_checker.py
```

## 📦 脚本说明

### 基础版本 (ip_reputation_recovery.py)
- **特点**: 轻量级，使用Python requests库
- **优势**: 资源占用少，稳定性高，适合长期运行
- **适用**: 轻度到中度IP信誉问题

### 高级版本 (ip_reputation_recovery_advanced.py)
- **特点**: 使用真实浏览器（Chrome + Selenium）
- **优势**: 更真实的用户行为模拟，效果更好
- **适用**: 重度IP信誉问题，需要更强的恢复效果

### IP信誉检测 (ip_reputation_checker.py)
- **功能**: 检查IP在各大安全厂商的信誉状态
- **支持**: AbuseIPDB、VirusTotal、Cisco Talos、Spamhaus等
- **模式**: 单次检查和持续监控

## 🔧 安装步骤

### 系统要求
- Debian 12 (推荐) 或其他Linux发行版
- Python 3.7+
- 网络连接

### 自动安装
```bash
# 克隆或下载脚本文件
git clone <repository_url>
cd IPpurifier

# 运行安装脚本
chmod +x install.sh
./install.sh
```

### 手动安装
```bash
# 安装Python依赖
pip3 install --user requests

# 如需高级版本，额外安装
sudo apt install -y google-chrome-stable chromium-driver
pip3 install --user selenium

# 创建工作目录
mkdir -p ~/ip_reputation_recovery
cp *.py ~/ip_reputation_recovery/
cp *.json ~/ip_reputation_recovery/
```

## 🎯 使用方法

### 方式一：交互式控制面板（推荐）
```bash
cd ~/ip_reputation_recovery
chmod +x start_recovery.sh
./start_recovery.sh
```

### 方式二：命令行直接运行

#### 基础版本
```bash
# 运行一次
python3 ip_reputation_recovery.py --once

# 持续运行
python3 ip_reputation_recovery.py --continuous
```

#### 高级版本
```bash
# 运行一次
python3 ip_reputation_recovery_advanced.py --once

# 持续运行
python3 ip_reputation_recovery_advanced.py --continuous
```

#### IP信誉检测
```bash
# 检查当前IP
python3 ip_reputation_checker.py

# 检查指定IP
python3 ip_reputation_checker.py --ip *******

# 监控模式
python3 ip_reputation_checker.py --monitor --interval 3600
```

### 方式三：systemd服务（推荐生产环境）
```bash
# 启动服务
sudo systemctl start ip-reputation-recovery

# 设置开机自启
sudo systemctl enable ip-reputation-recovery

# 查看状态
sudo systemctl status ip-reputation-recovery

# 查看日志
sudo journalctl -u ip-reputation-recovery -f
```

### 方式四：crontab定时任务
```bash
# 编辑crontab
crontab -e

# 添加任务（每小时运行一次基础版本）
0 * * * * cd ~/ip_reputation_recovery && python3 ip_reputation_recovery.py --once

# 或每2小时运行一次高级版本
0 */2 * * * cd ~/ip_reputation_recovery && python3 ip_reputation_recovery_advanced.py --once
```

## ⚙️ 配置说明

### 基础版本配置 (config.json)
```json
{
  "sites": ["网站列表"],
  "user_agents": ["User-Agent列表"],
  "min_delay": 5,           // 最小延迟（秒）
  "max_delay": 20,          // 最大延迟（秒）
  "timeout": 10,            // 请求超时（秒）
  "sites_per_session": 8,   // 每次会话访问网站数
  "session_interval": 3600  // 会话间隔（秒）
}
```

### 高级版本配置 (config_advanced.json)
```json
{
  "headless": true,         // 无头模式
  "window_size": "1920,1080",
  "scroll_behavior": true,  // 模拟滚动
  "random_clicks": true,    // 模拟点击
  "stay_time_min": 3,       // 最小停留时间
  "stay_time_max": 10       // 最大停留时间
}
```

## 📊 监控和日志

### 日志文件位置
- 基础版本: `~/ip_reputation_recovery/ip_recovery.log`
- 高级版本: `~/ip_reputation_recovery/ip_recovery_advanced.log`
- IP信誉检测: `~/ip_reputation_recovery/ip_reputation_check.log`

### 查看日志
```bash
# 实时查看日志
tail -f ~/ip_reputation_recovery/ip_recovery.log

# 查看系统服务日志
sudo journalctl -u ip-reputation-recovery -f
```

### 监控IP信誉变化
```bash
# 每天自动检查并记录
echo "0 9 * * * cd ~/ip_reputation_recovery && python3 ip_reputation_checker.py" | crontab -
```

## 🎯 最佳实践

### 轻度恢复策略（误报/轻微问题）
1. 使用基础版本
2. 每2-4小时运行一次
3. 持续1-2周
4. 每天检查一次IP信誉

### 中度恢复策略（多次举报）
1. 先运行基础版本1周
2. 然后切换到高级版本
3. 每1-2小时运行一次
4. 持续2-3周

### 重度恢复策略（严重黑名单）
1. 确保服务器完全清洁
2. 使用高级版本
3. 每小时运行一次
4. 持续4-6周
5. 配合人工申诉

### 维护策略（预防性）
1. 使用基础版本
2. 每天运行1-2次
3. 长期维护
4. 定期监控

## 🔧 故障排除

### 常见问题

#### 1. Chrome/ChromeDriver问题
```bash
# 重新安装Chrome
sudo apt remove google-chrome-stable
sudo apt install google-chrome-stable

# 检查ChromeDriver版本
chromium-driver --version
```

#### 2. 网络连接问题
```bash
# 测试网络连接
curl -I https://www.google.com

# 检查DNS
nslookup google.com
```

#### 3. 权限问题
```bash
# 检查文件权限
ls -la ~/ip_reputation_recovery/

# 修复权限
chmod +x ~/ip_reputation_recovery/*.py
```

#### 4. Python依赖问题
```bash
# 重新安装依赖
pip3 install --user --upgrade requests selenium
```

### 调试模式
```bash
# 启用详细日志
python3 ip_reputation_recovery.py --once --verbose

# 检查配置文件
python3 -m json.tool config.json
```

## ⚠️ 注意事项

### 安全要求
1. **清理服务器**: 确保已移除所有恶意软件、后门、僵尸程序
2. **关闭危险端口**: 停用不必要的服务和端口
3. **更新系统**: 保持系统和软件最新

### 使用限制
1. **适度频率**: 不要设置过于频繁的访问间隔
2. **合理期望**: IP信誉恢复需要时间，通常1-4周
3. **合规使用**: 仅用于合法IP信誉恢复

### 效果评估
1. **定期检测**: 使用IP信誉检测脚本监控进展
2. **多平台验证**: 在不同平台检查IP状态
3. **实际测试**: 测试邮件发送、网站访问等实际应用

### 法律合规
1. 仅访问公开可访问的网站
2. 模拟正常用户行为，不进行破坏性操作
3. 遵守目标网站的robots.txt和使用条款
4. 不得用于恶意目的

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查网络连接和系统环境
3. 确认配置文件格式正确
4. 参考故障排除章节

---

**免责声明**: 本脚本仅用于合法的IP信誉恢复目的。使用者需确保遵守相关法律法规和网站使用条款。
