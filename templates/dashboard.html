<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP信誉恢复控制面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .dashboard {
            padding: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        
        .status-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .control-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 24px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            margin-right: 5px;
        }
        
        .tab.active {
            background: #007bff;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .service-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .service-active {
            background: #28a745;
            color: white;
        }
        
        .service-inactive {
            background: #dc3545;
            color: white;
        }
        
        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ IP信誉恢复控制面板</h1>
            <p>统一管理和监控IP信誉恢复脚本</p>
        </div>
        
        <div class="dashboard">
            <!-- 系统状态 -->
            <div class="control-section">
                <h2 class="section-title">📊 系统状态</h2>
                <div class="status-grid" id="statusGrid">
                    <div class="status-card">
                        <h3>CPU使用率</h3>
                        <div class="status-value" id="cpuUsage">--</div>
                        <div class="status-label">处理器负载</div>
                    </div>
                    <div class="status-card">
                        <h3>内存使用率</h3>
                        <div class="status-value" id="memoryUsage">--</div>
                        <div class="status-label">系统内存</div>
                    </div>
                    <div class="status-card">
                        <h3>服务状态</h3>
                        <div class="status-value" id="serviceStatus">--</div>
                        <div class="status-label">systemd服务</div>
                    </div>
                    <div class="status-card">
                        <h3>运行进程</h3>
                        <div class="status-value" id="processCount">--</div>
                        <div class="status-label">脚本进程数</div>
                    </div>
                </div>
            </div>
            
            <!-- 脚本控制 -->
            <div class="control-section">
                <h2 class="section-title">🎮 脚本控制</h2>
                
                <h3>基础版本</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="runScript('basic', 'once')">运行一次</button>
                    <button class="btn btn-success" onclick="runScript('basic', 'continuous')">持续运行</button>
                    <button class="btn btn-danger" onclick="stopScript('basic')">停止运行</button>
                </div>
                
                <h3>高级版本</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="runScript('advanced', 'once')">运行一次</button>
                    <button class="btn btn-success" onclick="runScript('advanced', 'continuous')">持续运行</button>
                    <button class="btn btn-danger" onclick="stopScript('advanced')">停止运行</button>
                </div>
                
                <h3>IP信誉检测</h3>
                <div class="button-group">
                    <button class="btn btn-info" onclick="runScript('checker', 'once')">检查IP信誉</button>
                    <button class="btn btn-warning" onclick="runTest()">环境测试</button>
                </div>
            </div>
            
            <!-- 服务管理 -->
            <div class="control-section">
                <h2 class="section-title">⚙️ 服务管理</h2>
                <div class="button-group">
                    <button class="btn btn-success" onclick="serviceAction('start')">启动服务</button>
                    <button class="btn btn-danger" onclick="serviceAction('stop')">停止服务</button>
                    <button class="btn btn-warning" onclick="serviceAction('restart')">重启服务</button>
                    <button class="btn btn-info" onclick="serviceAction('enable')">开机自启</button>
                    <button class="btn btn-info" onclick="serviceAction('disable')">禁用自启</button>
                </div>
            </div>
            
            <!-- 日志查看 -->
            <div class="control-section">
                <h2 class="section-title">📋 日志查看</h2>
                <div class="tabs">
                    <button class="tab active" onclick="showTab('basic-log')">基础版本日志</button>
                    <button class="tab" onclick="showTab('advanced-log')">高级版本日志</button>
                    <button class="tab" onclick="showTab('checker-log')">检测日志</button>
                </div>
                
                <div id="basic-log" class="tab-content active">
                    <button class="btn btn-primary" onclick="loadLogs('basic')">刷新日志</button>
                    <div class="log-container" id="basicLogs">点击"刷新日志"查看内容...</div>
                </div>
                
                <div id="advanced-log" class="tab-content">
                    <button class="btn btn-primary" onclick="loadLogs('advanced')">刷新日志</button>
                    <div class="log-container" id="advancedLogs">点击"刷新日志"查看内容...</div>
                </div>
                
                <div id="checker-log" class="tab-content">
                    <button class="btn btn-primary" onclick="loadLogs('checker')">刷新日志</button>
                    <div class="log-container" id="checkerLogs">点击"刷新日志"查看内容...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 消息提示区域 -->
    <div id="messageArea"></div>

    <script>
        // 全局变量
        let statusInterval;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatus();
            startStatusUpdates();
        });
        
        // 开始状态更新
        function startStatusUpdates() {
            statusInterval = setInterval(loadStatus, 5000); // 每5秒更新一次
        }
        
        // 停止状态更新
        function stopStatusUpdates() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
        }
        
        // 加载系统状态
        function loadStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showMessage('获取状态失败: ' + data.error, 'danger');
                        return;
                    }
                    
                    document.getElementById('cpuUsage').textContent = data.cpu_percent + '%';
                    document.getElementById('memoryUsage').textContent = data.memory_percent + '%';
                    
                    const serviceStatus = document.getElementById('serviceStatus');
                    if (data.service_status.active) {
                        serviceStatus.innerHTML = '<span class="service-status service-active">运行中</span>';
                    } else {
                        serviceStatus.innerHTML = '<span class="service-status service-inactive">已停止</span>';
                    }
                    
                    document.getElementById('processCount').textContent = data.script_processes.length;
                })
                .catch(error => {
                    console.error('获取状态失败:', error);
                });
        }
        
        // 运行脚本
        function runScript(scriptType, mode) {
            showMessage('正在启动脚本...', 'info');
            
            fetch(`/api/run/${scriptType}?mode=${mode}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        loadStatus(); // 刷新状态
                    } else {
                        showMessage(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showMessage('请求失败: ' + error, 'danger');
                });
        }
        
        // 停止脚本
        function stopScript(scriptType) {
            showMessage('正在停止脚本...', 'info');
            
            fetch(`/api/stop/${scriptType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        loadStatus(); // 刷新状态
                    } else {
                        showMessage(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showMessage('请求失败: ' + error, 'danger');
                });
        }
        
        // 服务操作
        function serviceAction(action) {
            showMessage(`正在${action}服务...`, 'info');
            
            fetch(`/api/service/${action}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        loadStatus(); // 刷新状态
                    } else {
                        showMessage(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showMessage('请求失败: ' + error, 'danger');
                });
        }
        
        // 运行测试
        function runTest() {
            showMessage('正在运行环境测试...', 'info');
            
            fetch('/api/test')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('测试完成', 'success');
                        // 显示测试结果
                        const logContainer = document.getElementById('basicLogs');
                        logContainer.textContent = data.output;
                    } else {
                        showMessage('测试失败: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    showMessage('请求失败: ' + error, 'danger');
                });
        }
        
        // 加载日志
        function loadLogs(logType) {
            const logContainers = {
                'basic': 'basicLogs',
                'advanced': 'advancedLogs',
                'checker': 'checkerLogs'
            };
            
            const containerId = logContainers[logType];
            const container = document.getElementById(containerId);
            
            container.innerHTML = '<div class="loading"></div> 加载中...';
            
            fetch(`/api/logs/${logType}?lines=100`)
                .then(response => response.json())
                .then(data => {
                    container.textContent = data.content;
                    container.scrollTop = container.scrollHeight; // 滚动到底部
                })
                .catch(error => {
                    container.textContent = '加载日志失败: ' + error;
                });
        }
        
        // 切换标签页
        function showTab(tabId) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签内容
            document.getElementById(tabId).classList.add('active');
            
            // 添加active类到对应的标签
            event.target.classList.add('active');
        }
        
        // 显示消息
        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            // 添加到页面
            messageArea.appendChild(alertDiv);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }
        
        // 页面卸载时停止状态更新
        window.addEventListener('beforeunload', function() {
            stopStatusUpdates();
        });
    </script>
</body>
</html>
