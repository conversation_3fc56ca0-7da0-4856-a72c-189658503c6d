#!/bin/bash

# IP信誉恢复脚本安装器
# 适用于Debian 12系统

set -e

echo "=========================================="
echo "IP信誉恢复脚本安装器"
echo "适用于Debian 12系统"
echo "=========================================="

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo "警告: 检测到root用户，建议使用普通用户运行此脚本"
   read -p "是否继续? (y/N): " -n 1 -r
   echo
   if [[ ! $REPLY =~ ^[Yy]$ ]]; then
       exit 1
   fi
fi

# 更新系统包
echo "正在更新系统包..."
sudo apt update

# 安装Python3和pip（如果未安装）
echo "正在检查Python3和pip..."
if ! command -v python3 &> /dev/null; then
    echo "安装Python3..."
    sudo apt install -y python3
fi

if ! command -v pip3 &> /dev/null; then
    echo "安装pip3..."
    sudo apt install -y python3-pip
fi

# 安装必要的Python包
echo "正在安装Python依赖包..."
pip3 install --user requests

# 询问是否安装高级版本（Selenium）
echo ""
read -p "是否安装高级版本（需要Chrome和Selenium）? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在安装Chrome和Selenium..."

    # 安装Chrome
    if ! command -v google-chrome &> /dev/null; then
        echo "安装Google Chrome..."
        wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
        sudo apt update
        sudo apt install -y google-chrome-stable
    fi

    # 安装ChromeDriver
    echo "安装ChromeDriver..."
    sudo apt install -y chromium-driver

    # 安装Selenium
    pip3 install --user selenium

    INSTALL_ADVANCED=true
else
    INSTALL_ADVANCED=false
fi

# 创建工作目录
INSTALL_DIR="$HOME/ip_reputation_recovery"
echo "创建安装目录: $INSTALL_DIR"
mkdir -p "$INSTALL_DIR"

# 复制文件到安装目录
echo "复制脚本文件..."
cp ip_reputation_recovery.py "$INSTALL_DIR/"
cp ip_reputation_checker.py "$INSTALL_DIR/"
cp config.json "$INSTALL_DIR/"
cp IP_REPUTATION_RECOVERY_DESIGN.md "$INSTALL_DIR/"

if [ "$INSTALL_ADVANCED" = true ]; then
    cp ip_reputation_recovery_advanced.py "$INSTALL_DIR/"
fi

# 设置执行权限
chmod +x "$INSTALL_DIR/ip_reputation_recovery.py"
chmod +x "$INSTALL_DIR/ip_reputation_checker.py"
if [ "$INSTALL_ADVANCED" = true ]; then
    chmod +x "$INSTALL_DIR/ip_reputation_recovery_advanced.py"
fi

# 创建systemd服务文件
echo "创建systemd服务..."
sudo tee /etc/systemd/system/ip-reputation-recovery.service > /dev/null <<EOF
[Unit]
Description=IP Reputation Recovery Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$INSTALL_DIR
ExecStart=/usr/bin/python3 $INSTALL_DIR/ip_reputation_recovery.py --continuous
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 创建crontab任务示例
echo "创建crontab任务示例..."
cat > "$INSTALL_DIR/crontab_example.txt" <<EOF
# IP信誉恢复脚本 - 每小时运行一次
0 * * * * cd $INSTALL_DIR && /usr/bin/python3 ip_reputation_recovery.py --once

# 或者每2小时运行一次
# 0 */2 * * * cd $INSTALL_DIR && /usr/bin/python3 ip_reputation_recovery.py --once

# 或者每天运行一次（推荐用于轻度恢复）
# 0 9 * * * cd $INSTALL_DIR && /usr/bin/python3 ip_reputation_recovery.py --once
EOF

# 创建高级版本配置文件
if [ "$INSTALL_ADVANCED" = true ]; then
    cat > "$INSTALL_DIR/config_advanced.json" <<EOF
{
  "sites": [
    "https://www.google.com",
    "https://www.bing.com",
    "https://www.youtube.com",
    "https://www.amazon.com",
    "https://www.wikipedia.org",
    "https://www.github.com",
    "https://www.stackoverflow.com",
    "https://www.reddit.com",
    "https://news.ycombinator.com",
    "https://www.bbc.com"
  ],
  "user_agents": [
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0"
  ],
  "min_delay": 8,
  "max_delay": 25,
  "page_load_timeout": 15,
  "implicit_wait": 10,
  "log_file": "ip_recovery_advanced.log",
  "sites_per_session": 6,
  "session_interval": 3600,
  "headless": true,
  "window_size": "1920,1080",
  "scroll_behavior": true,
  "random_clicks": true,
  "stay_time_min": 3,
  "stay_time_max": 10
}
EOF
fi

# 创建使用说明
cat > "$INSTALL_DIR/README.md" <<EOF
# IP信誉恢复脚本使用说明

## 安装完成！

脚本已安装到: $INSTALL_DIR

## 脚本说明

### 1. 基础版本 (ip_reputation_recovery.py)
- 使用Python requests库
- 轻量级，资源占用少
- 适合长期运行

### 2. 高级版本 (ip_reputation_recovery_advanced.py)
$(if [ "$INSTALL_ADVANCED" = true ]; then echo "✅ 已安装"; else echo "❌ 未安装"; fi)
- 使用真实浏览器（Chrome）
- 更真实的用户行为模拟
- 资源占用较多，但效果更好

### 3. IP信誉检测 (ip_reputation_checker.py)
- 检查IP在各大安全厂商的信誉状态
- 支持监控模式

## 使用方法

### 基础版本使用
\`\`\`bash
cd $INSTALL_DIR

# 运行一次
python3 ip_reputation_recovery.py --once

# 持续运行
python3 ip_reputation_recovery.py --continuous
\`\`\`

$(if [ "$INSTALL_ADVANCED" = true ]; then cat <<'ADVANCED_USAGE'
### 高级版本使用
\`\`\`bash
cd $INSTALL_DIR

# 运行一次
python3 ip_reputation_recovery_advanced.py --once

# 持续运行
python3 ip_reputation_recovery_advanced.py --continuous
\`\`\`
ADVANCED_USAGE
fi)

### IP信誉检测
\`\`\`bash
cd $INSTALL_DIR

# 检查当前IP信誉
python3 ip_reputation_checker.py

# 检查指定IP
python3 ip_reputation_checker.py --ip *******

# 监控模式（每小时检查一次）
python3 ip_reputation_checker.py --monitor --interval 3600
\`\`\`

### 使用systemd服务（推荐）
\`\`\`bash
# 启动服务
sudo systemctl start ip-reputation-recovery

# 设置开机自启
sudo systemctl enable ip-reputation-recovery

# 查看服务状态
sudo systemctl status ip-reputation-recovery

# 查看日志
sudo journalctl -u ip-reputation-recovery -f

# 停止服务
sudo systemctl stop ip-reputation-recovery
\`\`\`

### 使用crontab定时任务
\`\`\`bash
# 编辑crontab
crontab -e

# 基础版本 - 每小时运行一次
0 * * * * cd $INSTALL_DIR && /usr/bin/python3 ip_reputation_recovery.py --once

$(if [ "$INSTALL_ADVANCED" = true ]; then cat <<'CRON_ADVANCED'
# 高级版本 - 每2小时运行一次（推荐）
0 */2 * * * cd $INSTALL_DIR && /usr/bin/python3 ip_reputation_recovery_advanced.py --once
CRON_ADVANCED
fi)

# IP信誉检测 - 每天检查一次
0 9 * * * cd $INSTALL_DIR && /usr/bin/python3 ip_reputation_checker.py
\`\`\`

## 配置文件

### 基础版本配置 (config.json)
- sites: 要访问的网站列表
- user_agents: User-Agent列表
- min_delay/max_delay: 访问间隔时间范围
- sites_per_session: 每次会话访问的网站数量
- session_interval: 会话间隔时间（秒）

$(if [ "$INSTALL_ADVANCED" = true ]; then cat <<'CONFIG_ADVANCED'
### 高级版本配置 (config_advanced.json)
- 包含基础版本所有配置
- headless: 是否无头模式运行
- scroll_behavior: 是否模拟滚动行为
- random_clicks: 是否模拟随机点击
- stay_time_min/max: 页面停留时间范围
CONFIG_ADVANCED
fi)

## 日志文件

- 基础版本: \`$INSTALL_DIR/ip_recovery.log\`
$(if [ "$INSTALL_ADVANCED" = true ]; then echo "- 高级版本: \`$INSTALL_DIR/ip_recovery_advanced.log\`"; fi)
- IP信誉检测: \`$INSTALL_DIR/ip_reputation_check.log\`

## 注意事项

1. **安全第一**: 确保服务器已清理所有恶意软件和后门
2. **适度使用**: 不要设置过于频繁的访问间隔，避免被识别为机器人
3. **定期监控**: 使用IP信誉检测脚本定期检查效果
4. **耐心等待**: IP信誉恢复通常需要1-4周时间
5. **合规使用**: 仅用于合法IP信誉恢复，不得用于恶意目的

## 推荐使用策略

### 轻度恢复（误报情况）
- 使用基础版本
- 每2-4小时运行一次
- 持续1-2周

### 重度恢复（曾被大量举报）
- 先使用基础版本运行1周
- 然后切换到高级版本
- 每1-2小时运行一次
- 持续2-4周

### 维护模式（预防性）
- 使用基础版本
- 每天运行1-2次
- 长期维护
EOF

echo "=========================================="
echo "安装完成！"
echo "=========================================="
echo "安装目录: $INSTALL_DIR"
echo ""
echo "快速开始:"
echo "1. 手动测试: cd $INSTALL_DIR && python3 ip_reputation_recovery.py --once"
echo "2. 启动服务: sudo systemctl start ip-reputation-recovery"
echo "3. 查看日志: tail -f $INSTALL_DIR/ip_recovery.log"
echo ""
echo "详细使用说明请查看: $INSTALL_DIR/README.md"
echo "=========================================="
