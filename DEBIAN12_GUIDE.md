# Debian 12 专用使用指南

## 🎯 Debian 12 特殊说明

Debian 12 引入了新的Python包管理策略，使用PEP 668标准来防止系统级Python环境被意外修改。这会导致`pip install`命令出现`externally-managed-environment`错误。

## 🚨 常见错误

```
error: externally-managed-environment

× This environment is externally managed
╰─> To install Python packages system-wide, try apt install
    python3-xyz, where xyz is the package you are trying to
    install.
```

## 🛠️ 解决方案

我们提供了三种解决方案，推荐按顺序尝试：

### 方案1: 一键修复脚本（推荐）

```bash
# 运行专门的Debian 12修复脚本
chmod +x fix_debian12.sh
./fix_debian12.sh

# 测试修复结果
python3 test_debian12.py
```

### 方案2: 手动安装系统包

```bash
# 安装系统级Python包
sudo apt update
sudo apt install -y python3-requests python3-venv

# 如需高级版本，安装Selenium相关包
sudo apt install -y python3-selenium google-chrome-stable chromium-driver
```

### 方案3: 使用虚拟环境

```bash
# 创建虚拟环境
python3 -m venv ~/ip_reputation_recovery_venv

# 激活虚拟环境
source ~/ip_reputation_recovery_venv/bin/activate

# 安装依赖
pip install requests selenium

# 使用完毕后退出
deactivate
```

## 🚀 推荐使用方式

修复完成后，推荐使用包装脚本，它们会自动选择最佳的Python环境：

### 基础版本
```bash
# 运行一次
./run_basic.sh --once

# 持续运行
./run_basic.sh --continuous
```

### 高级版本
```bash
# 运行一次
./run_advanced.sh --once

# 持续运行
./run_advanced.sh --continuous
```

### IP信誉检测
```bash
# 检查当前IP
./run_checker.sh

# 检查指定IP
./run_checker.sh --ip *******

# 监控模式
./run_checker.sh --monitor --interval 3600
```

## 🔧 systemd服务

修复脚本会自动更新systemd服务配置：

```bash
# 启动服务
sudo systemctl start ip-reputation-recovery

# 查看状态
sudo systemctl status ip-reputation-recovery

# 查看日志
sudo journalctl -u ip-reputation-recovery -f

# 开机自启
sudo systemctl enable ip-reputation-recovery
```

## 📊 环境测试

使用专门的Debian 12测试脚本：

```bash
python3 test_debian12.py
```

测试内容包括：
- 系统信息检查
- Python包可用性
- 虚拟环境状态
- Chrome/Selenium环境
- 网络连接
- 配置文件
- 包装脚本

## 🎯 使用策略

### 根据你的情况选择：

#### 情况1: 只需要基础功能
```bash
# 安装系统包
sudo apt install -y python3-requests

# 直接使用
python3 ip_reputation_recovery.py --once
```

#### 情况2: 需要高级功能
```bash
# 运行修复脚本
./fix_debian12.sh

# 使用包装脚本
./run_advanced.sh --once
```

#### 情况3: 长期运行
```bash
# 设置systemd服务
sudo systemctl enable ip-reputation-recovery
sudo systemctl start ip-reputation-recovery
```

## 🔍 故障排除

### 问题1: 包装脚本不存在
```bash
# 重新运行修复脚本
./fix_debian12.sh
```

### 问题2: Chrome相关错误
```bash
# 重新安装Chrome
sudo apt remove google-chrome-stable
sudo apt install -y google-chrome-stable chromium-driver
```

### 问题3: 虚拟环境问题
```bash
# 删除并重建虚拟环境
rm -rf ~/ip_reputation_recovery_venv
python3 -m venv ~/ip_reputation_recovery_venv
source ~/ip_reputation_recovery_venv/bin/activate
pip install requests selenium
deactivate
```

### 问题4: 权限问题
```bash
# 修复脚本权限
chmod +x *.sh *.py
```

## 📝 crontab配置

推荐的定时任务配置：

```bash
# 编辑crontab
crontab -e

# 基础版本 - 每2小时运行一次
0 */2 * * * cd /root/IPpurifier && ./run_basic.sh --once

# 高级版本 - 每小时运行一次（重度恢复）
0 * * * * cd /root/IPpurifier && ./run_advanced.sh --once

# IP信誉检测 - 每天检查一次
0 9 * * * cd /root/IPpurifier && ./run_checker.sh
```

## 🎉 验证安装

完成安装后，运行以下命令验证：

```bash
# 1. 测试环境
python3 test_debian12.py

# 2. 运行基础版本
./run_basic.sh --once

# 3. 检查IP信誉
./run_checker.sh

# 4. 查看日志
tail -f ip_recovery.log
```

如果所有步骤都成功，说明安装完成！

## 💡 最佳实践

1. **定期更新**: 定期运行`sudo apt update && sudo apt upgrade`
2. **监控日志**: 定期检查日志文件确保正常运行
3. **备份配置**: 备份`config.json`等配置文件
4. **测试网络**: 定期测试网络连接确保脚本能正常访问目标网站

## 📞 技术支持

如果遇到问题：

1. 运行`python3 test_debian12.py`检查环境
2. 查看日志文件获取详细错误信息
3. 尝试重新运行`./fix_debian12.sh`
4. 检查网络连接和防火墙设置

---

**注意**: 本指南专门针对Debian 12系统。其他Linux发行版请参考通用安装指南。
