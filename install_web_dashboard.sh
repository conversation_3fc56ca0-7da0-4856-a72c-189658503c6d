#!/bin/bash

# Web控制面板安装脚本

set -e

echo "🌐 安装IP信誉恢复Web控制面板"
echo "=================================="

# 检查Python版本
echo "🐍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python版本: $PYTHON_VERSION"

# 检查是否为Debian 12的外部管理环境
echo "📦 检查Python包管理..."
INSTALL_METHOD=""

if pip3 install --dry-run flask 2>&1 | grep -q "externally-managed-environment"; then
    echo "⚠️  检测到Debian 12外部管理环境"
    INSTALL_METHOD="system"
else
    INSTALL_METHOD="pip"
fi

# 安装依赖包
echo "📥 安装依赖包..."

if [ "$INSTALL_METHOD" = "system" ]; then
    echo "使用系统包管理器安装..."
    
    # 尝试安装系统级包
    sudo apt update
    
    # 安装Flask
    if sudo apt install -y python3-flask 2>/dev/null; then
        echo "✅ 已安装系统级python3-flask"
    else
        echo "⚠️  系统级python3-flask不可用，使用虚拟环境"
        INSTALL_METHOD="venv"
    fi
    
    # 安装psutil
    if sudo apt install -y python3-psutil 2>/dev/null; then
        echo "✅ 已安装系统级python3-psutil"
    else
        echo "⚠️  系统级python3-psutil不可用"
        if [ "$INSTALL_METHOD" != "venv" ]; then
            INSTALL_METHOD="venv"
        fi
    fi
fi

# 如果系统包不完整，使用虚拟环境
if [ "$INSTALL_METHOD" = "venv" ] || [ "$INSTALL_METHOD" = "pip" ]; then
    echo "使用虚拟环境安装..."
    
    # 确保有python3-venv
    sudo apt install -y python3-venv
    
    # 创建虚拟环境
    VENV_DIR="$HOME/web_dashboard_venv"
    if [ ! -d "$VENV_DIR" ]; then
        echo "创建虚拟环境: $VENV_DIR"
        python3 -m venv "$VENV_DIR"
    fi
    
    # 激活虚拟环境并安装包
    source "$VENV_DIR/bin/activate"
    pip install --upgrade pip
    pip install flask psutil
    deactivate
    
    echo "✅ 虚拟环境安装完成"
    
    # 创建虚拟环境启动脚本
    cat > "start_web_dashboard_venv.sh" <<EOF
#!/bin/bash
# Web控制面板启动脚本（虚拟环境版本）

SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$VENV_DIR"

echo "🌐 启动Web控制面板（虚拟环境）"
echo "虚拟环境: \$VENV_DIR"

if [ ! -d "\$VENV_DIR" ]; then
    echo "❌ 虚拟环境不存在: \$VENV_DIR"
    exit 1
fi

cd "\$SCRIPT_DIR"
source "\$VENV_DIR/bin/activate"
python web_dashboard.py
deactivate
EOF
    
    chmod +x start_web_dashboard_venv.sh
fi

# 创建标准启动脚本
cat > "start_web_dashboard.sh" <<EOF
#!/bin/bash
# Web控制面板启动脚本

SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
cd "\$SCRIPT_DIR"

echo "🌐 启动IP信誉恢复Web控制面板"
echo "访问地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务"
echo ""

# 检查依赖
if ! python3 -c "import flask, psutil" 2>/dev/null; then
    echo "❌ 缺少依赖包，请运行安装脚本"
    exit 1
fi

python3 web_dashboard.py
EOF

chmod +x start_web_dashboard.sh

# 创建systemd服务文件
echo "⚙️  创建systemd服务..."

CURRENT_DIR=$(pwd)
CURRENT_USER=$(whoami)

# 选择Python执行方式
if [ "$INSTALL_METHOD" = "venv" ]; then
    EXEC_START="$VENV_DIR/bin/python $CURRENT_DIR/web_dashboard.py"
else
    EXEC_START="/usr/bin/python3 $CURRENT_DIR/web_dashboard.py"
fi

sudo tee /etc/systemd/system/ip-reputation-web-dashboard.service > /dev/null <<EOF
[Unit]
Description=IP Reputation Recovery Web Dashboard
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$EXEC_START
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PYTHONUNBUFFERED=1
Environment=FLASK_ENV=production

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
echo "✅ systemd服务已创建"

# 设置防火墙（如果有）
echo "🔥 配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 5000/tcp
    echo "✅ UFW防火墙已配置"
elif command -v firewall-cmd &> /dev/null; then
    sudo firewall-cmd --permanent --add-port=5000/tcp
    sudo firewall-cmd --reload
    echo "✅ firewalld防火墙已配置"
else
    echo "⚠️  未检测到防火墙，请手动开放5000端口"
fi

# 测试安装
echo "🧪 测试安装..."
if python3 -c "import flask, psutil; print('✅ 依赖包正常')" 2>/dev/null; then
    echo "✅ Web控制面板安装成功"
else
    echo "❌ 依赖包测试失败"
    exit 1
fi

echo ""
echo "🎉 Web控制面板安装完成！"
echo "=================================="
echo ""
echo "启动方式："
echo ""
echo "1. 手动启动（前台运行）："
echo "   ./start_web_dashboard.sh"
echo ""

if [ -f "start_web_dashboard_venv.sh" ]; then
    echo "2. 虚拟环境启动："
    echo "   ./start_web_dashboard_venv.sh"
    echo ""
fi

echo "3. 系统服务启动（后台运行）："
echo "   sudo systemctl start ip-reputation-web-dashboard"
echo "   sudo systemctl enable ip-reputation-web-dashboard  # 开机自启"
echo ""
echo "访问地址："
echo "   本地访问: http://localhost:5000"
echo "   局域网访问: http://$(hostname -I | awk '{print $1}'):5000"
echo ""
echo "管理命令："
echo "   sudo systemctl status ip-reputation-web-dashboard   # 查看状态"
echo "   sudo systemctl stop ip-reputation-web-dashboard     # 停止服务"
echo "   sudo journalctl -u ip-reputation-web-dashboard -f   # 查看日志"
echo ""
echo "⚠️  安全提示："
echo "   - Web面板默认监听所有网络接口"
echo "   - 建议在生产环境中配置认证和HTTPS"
echo "   - 确保防火墙正确配置"
