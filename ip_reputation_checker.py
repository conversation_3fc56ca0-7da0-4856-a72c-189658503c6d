#!/usr/bin/env python3
"""
IP信誉检测脚本
用于检查IP在各大安全厂商数据库中的信誉状态
"""

import requests
import json
import time
import logging
import argparse
import sys
from datetime import datetime
import re

class IPReputationChecker:
    def __init__(self):
        self.setup_logging()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('ip_reputation_check.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_public_ip(self):
        """获取公网IP地址"""
        try:
            # 尝试多个IP检测服务
            ip_services = [
                'https://api.ipify.org',
                'https://ipinfo.io/ip',
                'https://icanhazip.com',
                'https://ident.me'
            ]
            
            for service in ip_services:
                try:
                    response = self.session.get(service, timeout=10)
                    if response.status_code == 200:
                        ip = response.text.strip()
                        # 验证IP格式
                        if re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
                            return ip
                except:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取公网IP失败: {e}")
            return None
    
    def check_abuseipdb(self, ip):
        """检查AbuseIPDB"""
        try:
            # 注意：这需要API密钥，这里只是示例
            # 实际使用时需要注册获取API密钥
            url = f"https://www.abuseipdb.com/check/{ip}"
            
            # 使用网页版本进行简单检查
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                if 'not found in our database' in content or 'no reports' in content:
                    return "清洁"
                elif 'confidence of abuse' in content or 'reported' in content:
                    return "可疑/已报告"
                else:
                    return "未知"
            else:
                return "检查失败"
                
        except Exception as e:
            self.logger.warning(f"AbuseIPDB检查失败: {e}")
            return "检查失败"
    
    def check_virustotal(self, ip):
        """检查VirusTotal"""
        try:
            url = f"https://www.virustotal.com/gui/ip-address/{ip}"
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                if 'no security vendors flagged this ip' in content:
                    return "清洁"
                elif 'security vendors flagged this ip' in content:
                    return "已标记"
                else:
                    return "未知"
            else:
                return "检查失败"
                
        except Exception as e:
            self.logger.warning(f"VirusTotal检查失败: {e}")
            return "检查失败"
    
    def check_talos(self, ip):
        """检查Cisco Talos"""
        try:
            url = f"https://talosintelligence.com/reputation_center/lookup?search={ip}"
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                if 'good' in content or 'neutral' in content:
                    return "良好"
                elif 'poor' in content or 'bad' in content:
                    return "差"
                else:
                    return "未知"
            else:
                return "检查失败"
                
        except Exception as e:
            self.logger.warning(f"Talos检查失败: {e}")
            return "检查失败"
    
    def check_spamhaus(self, ip):
        """检查Spamhaus（简化版本）"""
        try:
            # 这里使用简单的网页检查
            # 实际的Spamhaus检查通常通过DNS查询
            url = f"https://www.spamhaus.org/query/ip/{ip}"
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                if 'not listed' in content or 'no data' in content:
                    return "未列入"
                elif 'listed' in content or 'blocked' in content:
                    return "已列入黑名单"
                else:
                    return "未知"
            else:
                return "检查失败"
                
        except Exception as e:
            self.logger.warning(f"Spamhaus检查失败: {e}")
            return "检查失败"
    
    def check_ipinfo(self, ip):
        """检查IPInfo基本信息"""
        try:
            url = f"https://ipinfo.io/{ip}/json"
            
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "城市": data.get("city", "未知"),
                    "地区": data.get("region", "未知"),
                    "国家": data.get("country", "未知"),
                    "ISP": data.get("org", "未知"),
                    "时区": data.get("timezone", "未知")
                }
            else:
                return {"错误": "获取失败"}
                
        except Exception as e:
            self.logger.warning(f"IPInfo检查失败: {e}")
            return {"错误": str(e)}
    
    def comprehensive_check(self, ip=None):
        """综合检查IP信誉"""
        if not ip:
            self.logger.info("正在获取公网IP地址...")
            ip = self.get_public_ip()
            if not ip:
                self.logger.error("无法获取公网IP地址")
                return None
        
        self.logger.info(f"开始检查IP信誉: {ip}")
        self.logger.info("=" * 60)
        
        # 基本信息
        self.logger.info("获取IP基本信息...")
        ip_info = self.check_ipinfo(ip)
        
        print(f"\n📍 IP地址: {ip}")
        print("📋 基本信息:")
        for key, value in ip_info.items():
            print(f"   {key}: {value}")
        
        # 信誉检查
        print("\n🔍 信誉检查结果:")
        
        checks = [
            ("AbuseIPDB", self.check_abuseipdb),
            ("VirusTotal", self.check_virustotal),
            ("Cisco Talos", self.check_talos),
            ("Spamhaus", self.check_spamhaus)
        ]
        
        results = {}
        
        for name, check_func in checks:
            self.logger.info(f"正在检查 {name}...")
            result = check_func(ip)
            results[name] = result
            
            # 根据结果显示不同的图标
            if result in ["清洁", "良好", "未列入"]:
                icon = "✅"
            elif result in ["可疑/已报告", "已标记", "差", "已列入黑名单"]:
                icon = "❌"
            elif result == "未知":
                icon = "❓"
            else:
                icon = "⚠️"
            
            print(f"   {icon} {name}: {result}")
            
            # 避免请求过于频繁
            time.sleep(2)
        
        # 总结
        print(f"\n📊 检查完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存结果到文件
        report = {
            "ip": ip,
            "timestamp": datetime.now().isoformat(),
            "basic_info": ip_info,
            "reputation_results": results
        }
        
        with open(f"ip_reputation_report_{ip}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return results
    
    def monitor_mode(self, ip=None, interval=3600):
        """监控模式 - 定期检查IP信誉"""
        self.logger.info(f"启动监控模式，检查间隔: {interval} 秒")
        
        try:
            while True:
                self.comprehensive_check(ip)
                self.logger.info(f"等待下次检查... ({interval} 秒)")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.logger.info("监控已停止")

def main():
    parser = argparse.ArgumentParser(description='IP信誉检测脚本')
    parser.add_argument('--ip', '-i', help='指定要检查的IP地址（默认检查本机公网IP）')
    parser.add_argument('--monitor', '-m', action='store_true', help='监控模式')
    parser.add_argument('--interval', '-t', type=int, default=3600, help='监控间隔（秒，默认3600）')
    
    args = parser.parse_args()
    
    checker = IPReputationChecker()
    
    if args.monitor:
        checker.monitor_mode(args.ip, args.interval)
    else:
        checker.comprehensive_check(args.ip)

if __name__ == "__main__":
    main()
