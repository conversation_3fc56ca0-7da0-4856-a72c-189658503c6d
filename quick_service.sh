#!/bin/bash

# 快速创建systemd服务 - 适用于当前Debian 12环境

echo "🚀 快速创建IP信誉恢复systemd服务"
echo "=================================="

# 获取当前目录
CURRENT_DIR=$(pwd)
echo "当前目录: $CURRENT_DIR"

# 检查必要文件
if [ ! -f "ip_reputation_recovery.py" ]; then
    echo "❌ 错误: 未找到 ip_reputation_recovery.py"
    exit 1
fi

echo "✅ 找到主脚本文件"

# 创建服务文件
echo "📝 创建systemd服务文件..."

sudo tee /etc/systemd/system/ip-reputation-recovery.service > /dev/null <<EOF
[Unit]
Description=IP Reputation Recovery Service
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=$CURRENT_DIR
ExecStart=/usr/bin/python3 $CURRENT_DIR/ip_reputation_recovery.py --continuous
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
EOF

echo "✅ 服务文件已创建"

# 重新加载systemd
echo "🔄 重新加载systemd..."
sudo systemctl daemon-reload

# 检查服务状态
echo "🔍 检查服务文件..."
if sudo systemctl cat ip-reputation-recovery.service > /dev/null 2>&1; then
    echo "✅ 服务文件创建成功"
else
    echo "❌ 服务文件创建失败"
    exit 1
fi

echo ""
echo "🎉 服务创建完成！"
echo ""
echo "现在你可以使用以下命令:"
echo ""
echo "# 启动服务"
echo "sudo systemctl start ip-reputation-recovery"
echo ""
echo "# 查看状态"
echo "sudo systemctl status ip-reputation-recovery"
echo ""
echo "# 设置开机自启"
echo "sudo systemctl enable ip-reputation-recovery"
echo ""
echo "# 查看实时日志"
echo "sudo journalctl -u ip-reputation-recovery -f"
echo ""
echo "# 停止服务"
echo "sudo systemctl stop ip-reputation-recovery"
