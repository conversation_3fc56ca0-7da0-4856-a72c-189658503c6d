#!/usr/bin/env python3
"""
IP信誉恢复脚本基础功能测试
"""

import sys
import json
import requests
import time

def test_python_version():
    """测试Python版本"""
    print("🐍 Python版本检查...")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ Python版本符合要求")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7+")
        return False

def test_requests_library():
    """测试requests库"""
    print("\n📦 requests库检查...")
    try:
        import requests
        print(f"   版本: {requests.__version__}")
        print("   ✅ requests库可用")
        return True
    except ImportError:
        print("   ❌ requests库未安装")
        print("   请运行: pip3 install requests")
        return False

def test_selenium_library():
    """测试selenium库（可选）"""
    print("\n🌐 Selenium库检查...")
    try:
        import selenium
        print(f"   版本: {selenium.__version__}")
        print("   ✅ Selenium库可用")
        return True
    except ImportError:
        print("   ⚠️  Selenium库未安装（高级版本需要）")
        print("   安装命令: pip3 install selenium")
        return False

def test_config_file():
    """测试配置文件"""
    print("\n⚙️  配置文件检查...")
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['sites', 'user_agents', 'min_delay', 'max_delay']
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            print(f"   ❌ 配置文件缺少必要字段: {missing_keys}")
            return False
        
        print(f"   网站数量: {len(config['sites'])}")
        print(f"   User-Agent数量: {len(config['user_agents'])}")
        print("   ✅ 配置文件格式正确")
        return True
        
    except FileNotFoundError:
        print("   ❌ 配置文件不存在")
        return False
    except json.JSONDecodeError:
        print("   ❌ 配置文件JSON格式错误")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌍 网络连接测试...")
    
    test_urls = [
        "https://www.google.com",
        "https://www.bing.com",
        "https://httpbin.org/get"
    ]
    
    success_count = 0
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ {url} - 连接成功")
                success_count += 1
            else:
                print(f"   ⚠️  {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url} - 连接失败: {str(e)[:50]}")
    
    if success_count >= 2:
        print(f"   ✅ 网络连接正常 ({success_count}/{len(test_urls)})")
        return True
    else:
        print(f"   ❌ 网络连接异常 ({success_count}/{len(test_urls)})")
        return False

def test_user_agent_rotation():
    """测试User-Agent轮换"""
    print("\n🎭 User-Agent轮换测试...")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        user_agents = config['user_agents']
        
        if len(user_agents) < 3:
            print("   ⚠️  User-Agent数量较少，建议增加")
        
        # 测试不同User-Agent的请求
        test_url = "https://httpbin.org/headers"
        
        for i, ua in enumerate(user_agents[:3]):  # 只测试前3个
            try:
                headers = {'User-Agent': ua}
                response = requests.get(test_url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    print(f"   ✅ UA {i+1}: {ua[:50]}...")
                else:
                    print(f"   ⚠️  UA {i+1}: 请求失败")
                    
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"   ❌ UA {i+1}: 测试失败")
        
        print("   ✅ User-Agent轮换功能正常")
        return True
        
    except Exception as e:
        print(f"   ❌ User-Agent测试失败: {e}")
        return False

def test_basic_simulation():
    """测试基础模拟功能"""
    print("\n🎯 基础模拟功能测试...")
    
    try:
        # 导入主脚本
        sys.path.append('.')
        from ip_reputation_recovery import IPReputationRecovery
        
        # 创建实例
        recovery = IPReputationRecovery()
        
        # 测试单个网站访问
        test_site = "https://httpbin.org/get"
        print(f"   测试访问: {test_site}")
        
        result = recovery.visit_site(test_site)
        
        if result:
            print("   ✅ 基础模拟功能正常")
            return True
        else:
            print("   ❌ 基础模拟功能异常")
            return False
            
    except Exception as e:
        print(f"   ❌ 基础模拟测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 IP信誉恢复脚本基础功能测试")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("requests库", test_requests_library),
        ("Selenium库", test_selenium_library),
        ("配置文件", test_config_file),
        ("网络连接", test_network_connectivity),
        ("User-Agent轮换", test_user_agent_rotation),
        ("基础模拟功能", test_basic_simulation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ {test_name}测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！脚本可以正常使用")
        return True
    elif passed >= total - 1:  # Selenium是可选的
        print("✅ 基础功能正常，可以使用基础版本")
        return True
    else:
        print("❌ 存在问题，请检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
