#!/usr/bin/env python3
"""
IP信誉恢复脚本 - 高级版本（使用Selenium）
更真实的浏览器行为模拟
适用于Debian 12系统
"""

import json
import logging
import random
import time
import sys
import os
import argparse
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

class AdvancedIPReputationRecovery:
    def __init__(self, config_file="config_advanced.json"):
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.driver = None
        
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "sites": [
                "https://www.google.com",
                "https://www.bing.com",
                "https://www.youtube.com",
                "https://www.amazon.com",
                "https://www.wikipedia.org",
                "https://www.github.com",
                "https://www.stackoverflow.com",
                "https://www.reddit.com",
                "https://news.ycombinator.com",
                "https://www.bbc.com"
            ],
            "user_agents": [
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0"
            ],
            "min_delay": 8,
            "max_delay": 25,
            "page_load_timeout": 15,
            "implicit_wait": 10,
            "log_file": "ip_recovery_advanced.log",
            "sites_per_session": 6,
            "session_interval": 3600,
            "headless": True,
            "window_size": "1920,1080",
            "scroll_behavior": True,
            "random_clicks": True,
            "stay_time_min": 3,
            "stay_time_max": 10
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
        else:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            print(f"已创建默认配置文件: {config_file}")
            
        return default_config
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config['log_file'], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            
            if self.config['headless']:
                chrome_options.add_argument('--headless')
            
            # 基本选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument(f'--window-size={self.config["window_size"]}')
            
            # 随机User-Agent
            user_agent = random.choice(self.config['user_agents'])
            chrome_options.add_argument(f'--user-agent={user_agent}')
            
            # 禁用自动化检测
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 创建驱动
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置超时
            self.driver.set_page_load_timeout(self.config['page_load_timeout'])
            self.driver.implicitly_wait(self.config['implicit_wait'])
            
            self.logger.info(f"浏览器驱动初始化成功 | UA: {user_agent[:50]}...")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器驱动初始化失败: {e}")
            return False
    
    def simulate_human_behavior(self):
        """模拟人类浏览行为"""
        try:
            # 随机滚动
            if self.config['scroll_behavior']:
                scroll_count = random.randint(1, 3)
                for _ in range(scroll_count):
                    scroll_y = random.randint(200, 800)
                    self.driver.execute_script(f"window.scrollBy(0, {scroll_y});")
                    time.sleep(random.uniform(0.5, 2.0))
            
            # 随机点击（安全的元素）
            if self.config['random_clicks']:
                try:
                    # 尝试找到安全的可点击元素
                    clickable_elements = self.driver.find_elements(By.TAG_NAME, "a")[:5]
                    if clickable_elements:
                        element = random.choice(clickable_elements)
                        if element.is_displayed() and element.is_enabled():
                            # 只是hover，不实际点击
                            webdriver.ActionChains(self.driver).move_to_element(element).perform()
                            time.sleep(random.uniform(0.5, 1.5))
                except:
                    pass  # 忽略点击错误
            
            # 停留时间
            stay_time = random.uniform(
                self.config['stay_time_min'], 
                self.config['stay_time_max']
            )
            self.logger.info(f"页面停留 {stay_time:.1f} 秒")
            time.sleep(stay_time)
            
        except Exception as e:
            self.logger.warning(f"人类行为模拟失败: {e}")
    
    def visit_site(self, url):
        """访问单个网站"""
        try:
            self.logger.info(f"正在访问: {url}")
            
            # 访问网站
            self.driver.get(url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 获取页面标题
            title = self.driver.title[:50] if self.driver.title else "无标题"
            self.logger.info(f"页面加载成功: {title}")
            
            # 模拟人类行为
            self.simulate_human_behavior()
            
            return True
            
        except TimeoutException:
            self.logger.warning(f"页面加载超时: {url}")
            return False
        except WebDriverException as e:
            self.logger.warning(f"浏览器错误: {url} | {str(e)[:100]}")
            return False
        except Exception as e:
            self.logger.error(f"访问失败: {url} | {str(e)}")
            return False
    
    def run_session(self):
        """运行一次浏览会话"""
        self.logger.info("=" * 60)
        self.logger.info("开始新的高级浏览会话")
        
        # 初始化浏览器
        if not self.setup_driver():
            return 0, 0
        
        try:
            # 随机选择要访问的网站
            sites_to_visit = random.sample(
                self.config['sites'], 
                min(self.config['sites_per_session'], len(self.config['sites']))
            )
            
            # 随机打乱访问顺序
            random.shuffle(sites_to_visit)
            
            success_count = 0
            total_count = len(sites_to_visit)
            
            for i, site in enumerate(sites_to_visit, 1):
                self.logger.info(f"访问进度 ({i}/{total_count}): {site}")
                
                if self.visit_site(site):
                    success_count += 1
                
                # 随机延迟（除了最后一个网站）
                if i < total_count:
                    delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
                    self.logger.info(f"等待 {delay:.1f} 秒...")
                    time.sleep(delay)
            
            self.logger.info(f"会话完成: 成功访问 {success_count}/{total_count} 个网站")
            return success_count, total_count
            
        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已关闭")
    
    def run_continuous(self):
        """持续运行模式"""
        self.logger.info("启动高级持续运行模式")
        self.logger.info(f"会话间隔: {self.config['session_interval']} 秒")
        
        session_count = 0
        
        try:
            while True:
                session_count += 1
                self.logger.info(f"第 {session_count} 次高级会话")
                
                success, total = self.run_session()
                
                self.logger.info(f"等待下次会话... ({self.config['session_interval']} 秒)")
                time.sleep(self.config['session_interval'])
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在停止...")
        except Exception as e:
            self.logger.error(f"运行时错误: {e}")
    
    def run_once(self):
        """运行一次"""
        self.logger.info("运行单次高级浏览会话")
        return self.run_session()

def main():
    parser = argparse.ArgumentParser(description='IP信誉恢复脚本 - 高级版本')
    parser.add_argument('--config', '-c', default='config_advanced.json', help='配置文件路径')
    parser.add_argument('--continuous', '-d', action='store_true', help='持续运行模式')
    parser.add_argument('--once', '-o', action='store_true', help='运行一次后退出')
    
    args = parser.parse_args()
    
    # 检查Chrome和ChromeDriver
    try:
        import selenium
        print(f"Selenium版本: {selenium.__version__}")
    except ImportError:
        print("错误: 未安装Selenium，请运行: pip3 install selenium")
        sys.exit(1)
    
    # 创建恢复器实例
    recovery = AdvancedIPReputationRecovery(args.config)
    
    if args.continuous:
        recovery.run_continuous()
    elif args.once:
        recovery.run_once()
    else:
        recovery.run_once()

if __name__ == "__main__":
    main()
