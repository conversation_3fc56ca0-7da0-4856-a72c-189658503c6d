#!/bin/bash

# 快速Web控制面板设置 - 适用于当前Debian 12环境

echo "🚀 快速设置Web控制面板"
echo "======================"

# 检查当前环境
echo "📋 检查当前环境..."
if [ ! -f "ip_reputation_recovery.py" ]; then
    echo "❌ 未找到主脚本文件"
    exit 1
fi

echo "✅ 主脚本文件存在"

# 检查Python和requests（你已经有了）
if python3 -c "import requests" 2>/dev/null; then
    echo "✅ requests库可用"
else
    echo "❌ requests库不可用"
    exit 1
fi

# 安装Flask和psutil
echo "📦 安装Web面板依赖..."

# 尝试系统包安装
if sudo apt install -y python3-flask python3-psutil 2>/dev/null; then
    echo "✅ 系统包安装成功"
    USE_SYSTEM=true
else
    echo "⚠️  系统包安装失败，使用虚拟环境"
    USE_SYSTEM=false
    
    # 创建虚拟环境
    python3 -m venv web_venv
    source web_venv/bin/activate
    pip install flask psutil
    deactivate
    echo "✅ 虚拟环境创建成功"
fi

# 创建启动脚本
echo "📝 创建启动脚本..."

if [ "$USE_SYSTEM" = true ]; then
    cat > "start_web.sh" <<'EOF'
#!/bin/bash
echo "🌐 启动Web控制面板"
echo "访问地址: http://localhost:5000"
echo "局域网访问: http://$(hostname -I | awk '{print $1}'):5000"
echo "按 Ctrl+C 停止"
echo ""

cd "$(dirname "$0")"
python3 web_dashboard.py
EOF
else
    cat > "start_web.sh" <<'EOF'
#!/bin/bash
echo "🌐 启动Web控制面板（虚拟环境）"
echo "访问地址: http://localhost:5000"
echo "局域网访问: http://$(hostname -I | awk '{print $1}'):5000"
echo "按 Ctrl+C 停止"
echo ""

cd "$(dirname "$0")"
source web_venv/bin/activate
python web_dashboard.py
deactivate
EOF
fi

chmod +x start_web.sh

# 测试依赖
echo "🧪 测试依赖..."
if [ "$USE_SYSTEM" = true ]; then
    if python3 -c "import flask, psutil; print('✅ 依赖正常')" 2>/dev/null; then
        echo "✅ 系统环境测试通过"
    else
        echo "❌ 系统环境测试失败"
        exit 1
    fi
else
    source web_venv/bin/activate
    if python -c "import flask, psutil; print('✅ 依赖正常')" 2>/dev/null; then
        echo "✅ 虚拟环境测试通过"
    else
        echo "❌ 虚拟环境测试失败"
        exit 1
    fi
    deactivate
fi

# 配置防火墙
echo "🔥 配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 5000/tcp 2>/dev/null || true
    echo "✅ 防火墙配置完成"
else
    echo "⚠️  请手动开放5000端口"
fi

echo ""
echo "🎉 Web控制面板设置完成！"
echo "========================"
echo ""
echo "启动命令："
echo "  ./start_web.sh"
echo ""
echo "访问地址："
echo "  本地: http://localhost:5000"
echo "  局域网: http://$(hostname -I | awk '{print $1}'):5000"
echo ""
echo "功能特性："
echo "  ✅ 实时系统状态监控"
echo "  ✅ 脚本一键启动/停止"
echo "  ✅ 实时日志查看"
echo "  ✅ systemd服务管理"
echo "  ✅ IP信誉检测"
echo ""
echo "现在运行 ./start_web.sh 启动Web面板！"
