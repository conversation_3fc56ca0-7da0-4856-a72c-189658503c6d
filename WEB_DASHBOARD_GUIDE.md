# Web控制面板使用指南

## 🌐 概述

Web控制面板提供了一个直观的浏览器界面来管理和监控IP信誉恢复脚本，无需命令行操作。

## 🚀 快速开始

### 在你的Debian 12服务器上：

```bash
# 1. 快速安装Web面板
./quick_web_setup.sh

# 2. 启动Web面板
./start_web.sh
```

### 访问Web界面：

- **本地访问**: http://localhost:5000
- **局域网访问**: http://你的服务器IP:5000

## 📱 界面功能

### 1. 系统状态监控
- **CPU使用率**: 实时显示处理器负载
- **内存使用率**: 显示系统内存占用情况
- **服务状态**: systemd服务运行状态
- **运行进程**: 当前运行的脚本进程数量

### 2. 脚本控制
#### 基础版本控制
- **运行一次**: 执行一次基础版本脚本
- **持续运行**: 后台持续运行基础版本
- **停止运行**: 停止正在运行的基础版本

#### 高级版本控制
- **运行一次**: 执行一次高级版本脚本（需要Chrome）
- **持续运行**: 后台持续运行高级版本
- **停止运行**: 停止正在运行的高级版本

#### IP信誉检测
- **检查IP信誉**: 立即检查当前IP的信誉状态
- **环境测试**: 运行系统环境测试

### 3. 服务管理
- **启动服务**: 启动systemd服务
- **停止服务**: 停止systemd服务
- **重启服务**: 重启systemd服务
- **开机自启**: 设置开机自动启动
- **禁用自启**: 禁用开机自动启动

### 4. 日志查看
- **基础版本日志**: 查看基础版本运行日志
- **高级版本日志**: 查看高级版本运行日志
- **检测日志**: 查看IP信誉检测日志
- **实时刷新**: 点击刷新按钮获取最新日志

## 🔧 安装方式

### 方式1: 快速安装（推荐）
```bash
./quick_web_setup.sh
./start_web.sh
```

### 方式2: 完整安装
```bash
./install_web_dashboard.sh
./start_web_dashboard.sh
```

### 方式3: 系统服务运行
```bash
# 安装后设置为系统服务
sudo systemctl start ip-reputation-web-dashboard
sudo systemctl enable ip-reputation-web-dashboard
```

## 🌍 网络访问

### 本地访问
- 地址: http://localhost:5000
- 适用: 服务器本地使用

### 局域网访问
- 地址: http://服务器IP:5000
- 适用: 同一局域网内的其他设备

### 外网访问（需要额外配置）
```bash
# 配置防火墙开放5000端口
sudo ufw allow 5000/tcp

# 或者使用nginx反向代理（推荐）
sudo apt install nginx
# 配置nginx反向代理到localhost:5000
```

## 📊 使用场景

### 场景1: 日常监控
1. 打开Web面板
2. 查看系统状态
3. 检查脚本运行情况
4. 查看最新日志

### 场景2: 启动IP恢复
1. 点击"基础版本" → "持续运行"
2. 监控系统状态
3. 定期查看日志确认正常运行

### 场景3: 检查IP信誉
1. 点击"检查IP信誉"
2. 查看检测结果
3. 根据结果调整恢复策略

### 场景4: 服务管理
1. 使用"服务管理"区域
2. 启动/停止systemd服务
3. 设置开机自启

## 🔒 安全建议

### 基础安全
- Web面板默认只监听本地，相对安全
- 如需外网访问，建议配置认证

### 生产环境安全
```bash
# 1. 使用nginx反向代理
sudo apt install nginx

# 2. 配置SSL证书
# 3. 添加HTTP基础认证
# 4. 限制访问IP
```

### 防火墙配置
```bash
# 只允许特定IP访问
sudo ufw allow from ***********/24 to any port 5000

# 或者只允许本地访问
sudo ufw deny 5000/tcp
```

## 🛠️ 故障排除

### 问题1: 无法访问Web面板
```bash
# 检查服务是否运行
ps aux | grep web_dashboard

# 检查端口是否监听
netstat -tlnp | grep 5000

# 检查防火墙
sudo ufw status
```

### 问题2: 依赖包错误
```bash
# 重新安装依赖
./quick_web_setup.sh

# 或手动安装
sudo apt install python3-flask python3-psutil
```

### 问题3: 权限问题
```bash
# 检查文件权限
ls -la web_dashboard.py

# 修复权限
chmod +x web_dashboard.py start_web.sh
```

### 问题4: 端口冲突
```bash
# 检查端口占用
sudo lsof -i :5000

# 修改端口（编辑web_dashboard.py最后一行）
app.run(host='0.0.0.0', port=8080, debug=False)
```

## 📱 移动端访问

Web面板采用响应式设计，支持手机和平板访问：

1. 确保设备在同一局域网
2. 访问 http://服务器IP:5000
3. 界面会自动适配移动设备

## 🔄 自动化建议

### 配合crontab使用
```bash
# 定期检查Web面板状态
*/5 * * * * pgrep -f web_dashboard || /root/IPpurifier/start_web.sh &

# 定期重启Web面板（可选）
0 3 * * * pkill -f web_dashboard && sleep 5 && /root/IPpurifier/start_web.sh &
```

### 配合systemd使用
```bash
# 设置为系统服务，自动重启
sudo systemctl enable ip-reputation-web-dashboard
```

## 📞 技术支持

如遇问题：

1. 检查Web面板日志
2. 检查系统日志: `sudo journalctl -u ip-reputation-web-dashboard`
3. 确认网络连接和防火墙设置
4. 重新运行安装脚本

---

**提示**: Web面板提供了完整的可视化管理功能，是管理IP信誉恢复脚本的最佳方式。
