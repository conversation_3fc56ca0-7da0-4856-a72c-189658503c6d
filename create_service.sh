#!/bin/bash

# 创建systemd服务脚本

set -e

echo "=========================================="
echo "创建IP信誉恢复systemd服务"
echo "=========================================="

# 获取当前目录和用户
CURRENT_DIR=$(pwd)
CURRENT_USER=$(whoami)

# 如果是root用户，询问是否要为特定用户创建服务
if [ "$CURRENT_USER" = "root" ]; then
    echo "检测到root用户"
    read -p "请输入要运行服务的用户名（默认为root）: " SERVICE_USER
    SERVICE_USER=${SERVICE_USER:-root}
    
    # 如果指定了非root用户，调整目录路径
    if [ "$SERVICE_USER" != "root" ]; then
        CURRENT_DIR="/home/<USER>/ip_reputation_recovery"
        echo "服务将使用目录: $CURRENT_DIR"
        echo "请确保脚本文件已复制到该目录"
    fi
else
    SERVICE_USER=$CURRENT_USER
fi

echo "服务用户: $SERVICE_USER"
echo "工作目录: $CURRENT_DIR"

# 检查必要文件是否存在
if [ ! -f "$CURRENT_DIR/ip_reputation_recovery.py" ]; then
    echo "错误: 在 $CURRENT_DIR 中未找到 ip_reputation_recovery.py"
    echo "请确保脚本文件在正确的目录中"
    exit 1
fi

# 选择运行模式
echo ""
echo "选择服务运行模式:"
echo "1. 基础版本 (python3 ip_reputation_recovery.py)"
echo "2. 包装脚本 (./run_basic.sh) - 如果存在"
echo "3. 虚拟环境模式"

read -p "请选择 (1-3): " MODE_CHOICE

case $MODE_CHOICE in
    1)
        EXEC_START="/usr/bin/python3 $CURRENT_DIR/ip_reputation_recovery.py --continuous"
        SERVICE_DESC="IP Reputation Recovery Service (Basic)"
        ;;
    2)
        if [ -f "$CURRENT_DIR/run_basic.sh" ]; then
            EXEC_START="$CURRENT_DIR/run_basic.sh --continuous"
            SERVICE_DESC="IP Reputation Recovery Service (Wrapper)"
        else
            echo "错误: run_basic.sh 不存在，请先运行 fix_debian12.sh"
            exit 1
        fi
        ;;
    3)
        VENV_PATH="/home/<USER>/ip_reputation_recovery_venv"
        if [ "$SERVICE_USER" = "root" ]; then
            VENV_PATH="/root/ip_reputation_recovery_venv"
        fi
        
        if [ ! -d "$VENV_PATH" ]; then
            echo "错误: 虚拟环境不存在: $VENV_PATH"
            echo "请先运行 fix_debian12.sh 创建虚拟环境"
            exit 1
        fi
        
        EXEC_START="$VENV_PATH/bin/python $CURRENT_DIR/ip_reputation_recovery.py --continuous"
        SERVICE_DESC="IP Reputation Recovery Service (Virtual Env)"
        ;;
    *)
        echo "无效选择，使用默认基础模式"
        EXEC_START="/usr/bin/python3 $CURRENT_DIR/ip_reputation_recovery.py --continuous"
        SERVICE_DESC="IP Reputation Recovery Service (Basic)"
        ;;
esac

echo ""
echo "创建systemd服务文件..."

# 创建服务文件
sudo tee /etc/systemd/system/ip-reputation-recovery.service > /dev/null <<EOF
[Unit]
Description=$SERVICE_DESC
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$EXEC_START
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PYTHONUNBUFFERED=1
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$CURRENT_DIR

[Install]
WantedBy=multi-user.target
EOF

echo "✅ 服务文件已创建: /etc/systemd/system/ip-reputation-recovery.service"

# 重新加载systemd
echo "重新加载systemd配置..."
sudo systemctl daemon-reload

# 检查服务文件语法
echo "检查服务文件..."
if sudo systemctl cat ip-reputation-recovery.service > /dev/null 2>&1; then
    echo "✅ 服务文件语法正确"
else
    echo "❌ 服务文件语法错误"
    exit 1
fi

# 询问是否立即启动服务
echo ""
read -p "是否立即启动服务? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "启动服务..."
    sudo systemctl start ip-reputation-recovery
    
    # 检查服务状态
    sleep 2
    if sudo systemctl is-active --quiet ip-reputation-recovery; then
        echo "✅ 服务启动成功"
    else
        echo "❌ 服务启动失败，查看状态:"
        sudo systemctl status ip-reputation-recovery
    fi
fi

# 询问是否设置开机自启
echo ""
read -p "是否设置开机自启? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "设置开机自启..."
    sudo systemctl enable ip-reputation-recovery
    echo "✅ 已设置开机自启"
fi

echo ""
echo "=========================================="
echo "服务创建完成！"
echo "=========================================="
echo ""
echo "常用命令:"
echo "  sudo systemctl start ip-reputation-recovery    # 启动服务"
echo "  sudo systemctl stop ip-reputation-recovery     # 停止服务"
echo "  sudo systemctl restart ip-reputation-recovery  # 重启服务"
echo "  sudo systemctl status ip-reputation-recovery   # 查看状态"
echo "  sudo systemctl enable ip-reputation-recovery   # 开机自启"
echo "  sudo systemctl disable ip-reputation-recovery  # 禁用自启"
echo ""
echo "查看日志:"
echo "  sudo journalctl -u ip-reputation-recovery -f   # 实时日志"
echo "  sudo journalctl -u ip-reputation-recovery -n 50 # 最近50行"
echo ""
echo "服务配置文件: /etc/systemd/system/ip-reputation-recovery.service"
echo "工作目录: $CURRENT_DIR"
echo "运行用户: $SERVICE_USER"
